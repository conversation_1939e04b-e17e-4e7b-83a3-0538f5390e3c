/**
 * 测试链接解析功能
 * 验证小红书和抖音链接的解析能力
 */

const cooperationService = require('../src/services/CooperationService');

async function testLinkParsing() {
  console.log('🔍 测试链接解析功能...\n');

  // 测试用例
  const testCases = [
    // 小红书链接测试
    {
      name: '小红书标准链接',
      link: 'https://www.xiaohongshu.com/explore/686e38b60000000011001e15?xsec_token=abc123',
      expectedPlatform: 'xiaohongshu',
      expectedNoteId: '686e38b60000000011001e15'
    },
    {
      name: '小红书简单链接',
      link: 'https://www.xiaohongshu.com/explore/64a1b2c3d4e5f6789abcdef0',
      expectedPlatform: 'xiaohongshu',
      expectedNoteId: '64a1b2c3d4e5f6789abcdef0'
    },
    
    // 抖音链接测试
    {
      name: '抖音用户页面带modal_id',
      link: 'https://www.douyin.com/user/MS4wLjABAAAARynxKPWVed9F9R3IKa4zLre7w5g9aAvgQw7nW5GIm9E?modal_id=7525784126083960127',
      expectedPlatform: 'juxingtu',
      expectedNoteId: '7525784126083960127'
    },
    {
      name: '抖音视频直链',
      link: 'https://www.douyin.com/video/7525784126083960127',
      expectedPlatform: 'juxingtu',
      expectedNoteId: '7525784126083960127'
    },
    {
      name: '抖音短链接',
      link: 'https://v.douyin.com/ieFyMJo/7525784126083960127',
      expectedPlatform: 'juxingtu',
      expectedNoteId: '7525784126083960127'
    },
    {
      name: '抖音链接带其他参数',
      link: 'https://www.douyin.com/user/test?from=share&modal_id=7525784126083960127&other=param',
      expectedPlatform: 'juxingtu',
      expectedNoteId: '7525784126083960127'
    },
    
    // 无效链接测试
    {
      name: '无效链接 - 空字符串',
      link: '',
      shouldFail: true
    },
    {
      name: '无效链接 - 不支持的平台',
      link: 'https://www.bilibili.com/video/BV1234567890',
      shouldFail: true
    },
    {
      name: '无效链接 - 格式错误',
      link: 'https://www.xiaohongshu.com/invalid/format',
      shouldFail: true
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  console.log(`📋 开始测试 ${totalTests} 个测试用例...\n`);

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`测试 ${i + 1}/${totalTests}: ${testCase.name}`);
    console.log(`链接: ${testCase.link}`);

    try {
      const result = cooperationService.parseNoteLink(testCase.link);
      
      if (testCase.shouldFail) {
        console.log('❌ 测试失败: 应该抛出错误但没有');
      } else {
        // 验证解析结果
        const platformMatch = result.platform === testCase.expectedPlatform;
        const noteIdMatch = result.noteId === testCase.expectedNoteId;
        const isValidMatch = result.isValid === true;

        if (platformMatch && noteIdMatch && isValidMatch) {
          console.log('✅ 测试通过');
          console.log(`   平台: ${result.platform}`);
          console.log(`   笔记ID: ${result.noteId}`);
          passedTests++;
        } else {
          console.log('❌ 测试失败: 解析结果不匹配');
          console.log(`   期望平台: ${testCase.expectedPlatform}, 实际: ${result.platform}`);
          console.log(`   期望笔记ID: ${testCase.expectedNoteId}, 实际: ${result.noteId}`);
          console.log(`   期望有效性: true, 实际: ${result.isValid}`);
        }
      }
    } catch (error) {
      if (testCase.shouldFail) {
        console.log('✅ 测试通过: 正确抛出错误');
        console.log(`   错误信息: ${error.message}`);
        passedTests++;
      } else {
        console.log('❌ 测试失败: 意外抛出错误');
        console.log(`   错误信息: ${error.message}`);
      }
    }

    console.log(''); // 空行分隔
  }

  // 测试结果汇总
  console.log('📊 测试结果汇总:');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过: ${passedTests}`);
  console.log(`失败: ${totalTests - passedTests}`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！链接解析功能正常工作。');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查链接解析逻辑。');
  }

  return {
    total: totalTests,
    passed: passedTests,
    failed: totalTests - passedTests,
    successRate: ((passedTests / totalTests) * 100).toFixed(1)
  };
}

// 运行测试
if (require.main === module) {
  testLinkParsing()
    .then(result => {
      process.exit(result.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = testLinkParsing;
