/**
 * 测试巨量星图定时数据拉取功能
 * 验证定时任务机制、多平台支持和任务筛选逻辑
 */

const cooperationService = require('../src/services/CooperationService');
const cooperationScheduleService = require('../src/services/CooperationScheduleService');
const { CooperationManagement } = require('../src/models');
const moment = require('moment');

async function testJuxingtuScheduledFetch() {
  console.log('🔍 测试巨量星图定时数据拉取功能...\n');

  try {
    // 1. 测试Cookie状态检查（多平台支持）
    console.log('📋 测试1: Cookie状态检查（多平台支持）');
    
    const cookieStatus = await cooperationScheduleService.checkCookieStatus();
    console.log('✅ Cookie状态检查结果:');
    console.log(`   整体状态: ${cookieStatus.hasAvailable ? '有可用Cookie' : '无可用Cookie'}`);
    console.log(`   状态信息: ${cookieStatus.message}`);
    
    if (cookieStatus.platforms) {
      console.log('   各平台详情:');
      for (const [platform, status] of Object.entries(cookieStatus.platforms)) {
        const platformName = platform === 'xiaohongshu' ? '小红书' : '巨量星图';
        const statusText = status.hasAvailable ? `✅ ${status.accountName}` : '❌ 无可用Cookie';
        console.log(`     ${platformName}: ${statusText}`);
      }
    }
    console.log('');

    // 2. 测试获取需要拉取数据的记录（包含巨量星图）
    console.log('📋 测试2: 获取需要拉取数据的记录');
    
    const recordsForFetch = await cooperationService.getRecordsForDataFetch();
    console.log(`✅ 找到 ${recordsForFetch.length} 条需要拉取数据的记录`);
    
    // 按平台分组统计
    const platformStats = {};
    recordsForFetch.forEach(record => {
      const platform = record.platform || 'unknown';
      platformStats[platform] = (platformStats[platform] || 0) + 1;
    });
    
    console.log('   按平台分组统计:');
    for (const [platform, count] of Object.entries(platformStats)) {
      const platformName = platform === 'xiaohongshu' ? '小红书' : 
                          platform === 'juxingtu' ? '巨量星图' : platform;
      console.log(`     ${platformName}: ${count}条`);
    }
    
    // 显示前几条记录的详情
    if (recordsForFetch.length > 0) {
      console.log('   前5条记录详情:');
      recordsForFetch.slice(0, 5).forEach((record, index) => {
        const platformName = record.platform === 'xiaohongshu' ? '小红书' : 
                            record.platform === 'juxingtu' ? '巨量星图' : record.platform;
        console.log(`     ${index + 1}. ID:${record.id}, 客户:${record.customerName}, 平台:${platformName}, 笔记ID:${record.noteId}, 状态:${record.dataFetchStatus}`);
      });
    }
    console.log('');

    // 3. 测试数据拉取任务面板显示
    console.log('📋 测试3: 数据拉取任务面板显示');
    
    const taskList = await cooperationService.getDataFetchTasks({
      page: 1,
      limit: 10
    });
    
    console.log(`✅ 任务面板显示 ${taskList.tasks.length} 条记录`);
    
    // 按平台统计任务面板中的记录
    const panelPlatformStats = {};
    taskList.tasks.forEach(task => {
      const platform = task.platform || 'unknown';
      panelPlatformStats[platform] = (panelPlatformStats[platform] || 0) + 1;
    });
    
    console.log('   任务面板按平台统计:');
    for (const [platform, count] of Object.entries(panelPlatformStats)) {
      const platformName = platform === 'xiaohongshu' ? '小红书' : 
                          platform === 'juxingtu' ? '巨量星图' : platform;
      console.log(`     ${platformName}: ${count}条`);
    }
    console.log('');

    // 4. 测试统计数据（多平台支持）
    console.log('📋 测试4: 统计数据（多平台支持）');
    
    const stats = await cooperationService.getDataFetchTaskStats();
    console.log('✅ 统计数据:');
    console.log(`   总数: ${stats.total}`);
    console.log(`   待执行: ${stats.pending}`);
    console.log(`   拉取中: ${stats.fetching}`);
    console.log(`   成功: ${stats.success}`);
    console.log(`   失败: ${stats.failed}`);
    console.log(`   今日可执行: ${stats.todayExecutable}`);
    console.log(`   今日已执行: ${stats.todayExecuted}`);
    console.log('');

    // 5. 测试定时任务状态
    console.log('📋 测试5: 定时任务状态');
    
    const taskStatus = cooperationScheduleService.getTaskStatus();
    console.log('✅ 定时任务状态:');
    console.log(`   已初始化: ${taskStatus.initialized ? '是' : '否'}`);
    console.log(`   任务总数: ${taskStatus.totalTasks}`);
    
    if (taskStatus.tasks.length > 0) {
      console.log('   任务详情:');
      taskStatus.tasks.forEach(task => {
        console.log(`     - ${task.name}: 运行中=${task.running}, 已调度=${task.scheduled}, 已销毁=${task.destroyed}`);
      });
    }
    console.log('');

    // 6. 测试巨量星图记录的时间计算
    console.log('📋 测试6: 巨量星图记录的时间计算');
    
    const juxingtuRecords = recordsForFetch.filter(record => record.platform === 'juxingtu');
    if (juxingtuRecords.length > 0) {
      console.log(`✅ 找到 ${juxingtuRecords.length} 条巨量星图记录`);
      
      juxingtuRecords.slice(0, 3).forEach((record, index) => {
        const now = moment();
        const scheduledTime = moment(record.scheduledFetchTime);
        const canFetch = now.isAfter(scheduledTime);
        
        console.log(`   ${index + 1}. 记录ID: ${record.id}`);
        console.log(`      客户名称: ${record.customerName}`);
        console.log(`      笔记ID: ${record.noteId}`);
        console.log(`      定时拉取时间: ${record.scheduledFetchTime}`);
        console.log(`      当前时间: ${now.toISOString()}`);
        console.log(`      可以拉取: ${canFetch ? '✅ 是' : '❌ 否'}`);
      });
    } else {
      console.log('⚠️ 没有找到巨量星图记录，可能需要先创建测试数据');
    }
    console.log('');

    // 7. 验证方法兼容性
    console.log('📋 测试7: 方法兼容性验证');
    
    const methodChecks = {
      'fetchNoteData': typeof cooperationService.fetchNoteData === 'function',
      'fetchXiaohongshuNoteData': typeof cooperationService.fetchXiaohongshuNoteData === 'function',
      'fetchJuxingtuNoteData': typeof cooperationService.fetchJuxingtuNoteData === 'function',
      'getRecordsForDataFetch': typeof cooperationService.getRecordsForDataFetch === 'function',
      'getDataFetchTasks': typeof cooperationService.getDataFetchTasks === 'function',
      'getDataFetchTaskStats': typeof cooperationService.getDataFetchTaskStats === 'function'
    };
    
    console.log('✅ 方法兼容性检查:');
    for (const [method, exists] of Object.entries(methodChecks)) {
      console.log(`   ${method}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
    }

    console.log('\n🎉 巨量星图定时数据拉取功能测试完成!');
    
    return {
      cookieStatusCheck: !!cookieStatus.platforms,
      recordsFetch: recordsForFetch.length >= 0,
      taskPanelDisplay: taskList.tasks.length >= 0,
      statsGeneration: !!stats,
      scheduledTaskStatus: taskStatus.initialized,
      juxingtuRecordsFound: juxingtuRecords ? juxingtuRecords.length > 0 : false,
      methodCompatibility: Object.values(methodChecks).every(v => v)
    };

  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testJuxingtuScheduledFetch()
    .then(result => {
      console.log('\n📊 测试结果摘要:');
      console.log(`- Cookie状态检查: ${result.cookieStatusCheck ? '✅' : '❌'}`);
      console.log(`- 记录获取: ${result.recordsFetch ? '✅' : '❌'}`);
      console.log(`- 任务面板显示: ${result.taskPanelDisplay ? '✅' : '❌'}`);
      console.log(`- 统计数据生成: ${result.statsGeneration ? '✅' : '❌'}`);
      console.log(`- 定时任务状态: ${result.scheduledTaskStatus ? '✅' : '❌'}`);
      console.log(`- 巨量星图记录: ${result.juxingtuRecordsFound ? '✅ 找到记录' : '⚠️ 无记录'}`);
      console.log(`- 方法兼容性: ${result.methodCompatibility ? '✅' : '❌'}`);
      
      const criticalTests = [
        result.cookieStatusCheck,
        result.recordsFetch,
        result.taskPanelDisplay,
        result.statsGeneration,
        result.scheduledTaskStatus,
        result.methodCompatibility
      ];
      
      const allCriticalPassed = criticalTests.every(v => v);
      console.log(`\n整体测试结果: ${allCriticalPassed ? '✅ 核心功能正常' : '❌ 部分功能异常'}`);
      
      process.exit(allCriticalPassed ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = testJuxingtuScheduledFetch;
