/**
 * 测试任务列表排序的脚本
 * 验证数据登记日期倒序排列
 */

const cooperationService = require('../src/services/CooperationService');

async function testTaskListSorting() {
  console.log('🔍 测试任务列表排序...\n');

  try {
    // 获取任务列表
    console.log('📋 获取任务列表数据...');
    const taskList = await cooperationService.getDataFetchTasks({
      page: 1,
      limit: 10 // 获取前10条记录用于验证排序
    });

    console.log(`任务列表结果: 总数=${taskList.pagination.totalCount}, 当前页=${taskList.tasks.length}条\n`);

    // 显示任务列表的排序情况
    console.log('📅 任务列表按数据登记日期排序情况:');
    console.log('序号 | 客户名称 | 数据登记日期 | 笔记ID | 状态');
    console.log('-'.repeat(80));

    taskList.tasks.forEach((task, index) => {
      const hasNoteId = task.noteId ? '✅' : '❌';
      const registrationDate = task.dataRegistrationDate || '未设置';
      const customerName = (task.customerName || '未知客户').padEnd(12);
      const status = task.dataFetchStatus || 'unknown';
      
      console.log(`${(index + 1).toString().padStart(2)} | ${customerName} | ${registrationDate} | ${hasNoteId} | ${status}`);
    });

    // 验证排序逻辑
    console.log('\n🔍 验证排序逻辑...');
    
    // 1. 验证有noteId的记录是否优先显示
    const tasksWithNoteId = taskList.tasks.filter(task => task.noteId);
    const tasksWithoutNoteId = taskList.tasks.filter(task => !task.noteId);
    
    console.log(`有noteId的记录数: ${tasksWithNoteId.length}`);
    console.log(`无noteId的记录数: ${tasksWithoutNoteId.length}`);
    
    // 2. 验证数据登记日期是否按倒序排列
    let isDescendingOrder = true;
    let dateOrderInfo = [];
    
    for (let i = 0; i < taskList.tasks.length - 1; i++) {
      const currentTask = taskList.tasks[i];
      const nextTask = taskList.tasks[i + 1];
      
      // 跳过没有数据登记日期的记录
      if (!currentTask.dataRegistrationDate || !nextTask.dataRegistrationDate) {
        continue;
      }
      
      const currentDate = new Date(currentTask.dataRegistrationDate);
      const nextDate = new Date(nextTask.dataRegistrationDate);
      
      dateOrderInfo.push({
        index: i,
        current: currentTask.dataRegistrationDate,
        next: nextTask.dataRegistrationDate,
        isCorrectOrder: currentDate >= nextDate
      });
      
      if (currentDate < nextDate) {
        isDescendingOrder = false;
      }
    }
    
    console.log(`\n数据登记日期倒序排列: ${isDescendingOrder ? '✅ 正确' : '❌ 错误'}`);
    
    if (!isDescendingOrder) {
      console.log('排序详情:');
      dateOrderInfo.forEach(info => {
        const status = info.isCorrectOrder ? '✅' : '❌';
        console.log(`  ${info.index}-${info.index + 1}: ${info.current} -> ${info.next} ${status}`);
      });
    }

    // 3. 验证优先级排序（有noteId的记录在前）
    let priorityCorrect = true;
    let firstWithoutNoteIdIndex = -1;
    
    for (let i = 0; i < taskList.tasks.length; i++) {
      if (!taskList.tasks[i].noteId) {
        firstWithoutNoteIdIndex = i;
        break;
      }
    }
    
    if (firstWithoutNoteIdIndex > -1) {
      // 检查在第一个没有noteId的记录之后，是否还有有noteId的记录
      for (let i = firstWithoutNoteIdIndex + 1; i < taskList.tasks.length; i++) {
        if (taskList.tasks[i].noteId) {
          priorityCorrect = false;
          break;
        }
      }
    }
    
    console.log(`优先级排序(有noteId优先): ${priorityCorrect ? '✅ 正确' : '❌ 错误'}`);

    console.log('\n🎉 排序测试完成!');
    
    return {
      totalTasks: taskList.pagination.totalCount,
      currentPageTasks: taskList.tasks.length,
      hasNoteIdTasks: tasksWithNoteId.length,
      noNoteIdTasks: tasksWithoutNoteId.length,
      isDescendingOrder,
      priorityCorrect,
      sortingCorrect: isDescendingOrder && priorityCorrect
    };

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testTaskListSorting()
    .then(result => {
      console.log('\n📋 排序测试结果摘要:');
      console.log(`- 总任务数: ${result.totalTasks}`);
      console.log(`- 当前页任务数: ${result.currentPageTasks}`);
      console.log(`- 有noteId任务数: ${result.hasNoteIdTasks}`);
      console.log(`- 无noteId任务数: ${result.noNoteIdTasks}`);
      console.log(`- 日期倒序排列: ${result.isDescendingOrder ? '✅' : '❌'}`);
      console.log(`- 优先级排序: ${result.priorityCorrect ? '✅' : '❌'}`);
      console.log(`- 整体排序正确: ${result.sortingCorrect ? '✅' : '❌'}`);
      
      process.exit(result.sortingCorrect ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = testTaskListSorting;
