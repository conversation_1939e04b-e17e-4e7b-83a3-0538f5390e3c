/**
 * 测试API端点的脚本
 * 直接调用服务层方法，绕过认证
 */

const cooperationService = require('../src/services/CooperationService');

async function testAPIEndpoints() {
  console.log('🔍 测试API端点...\n');

  try {
    // 1. 测试统计API
    console.log('📊 测试统计API...');
    const stats = await cooperationService.getDataFetchTaskStats();
    console.log('统计结果:', JSON.stringify(stats, null, 2));

    // 2. 测试任务列表API
    console.log('\n📋 测试任务列表API...');
    const taskList = await cooperationService.getDataFetchTasks({
      page: 1,
      limit: 20
    });
    console.log(`任务列表结果: 总数=${taskList.pagination.totalCount}, 当前页=${taskList.tasks.length}条`);
    
    // 按状态统计任务列表
    const tasksByStatus = {
      pending: taskList.tasks.filter(task => task.dataFetchStatus === 'pending').length,
      fetching: taskList.tasks.filter(task => task.dataFetchStatus === 'fetching').length,
      success: taskList.tasks.filter(task => task.dataFetchStatus === 'success').length,
      failed: taskList.tasks.filter(task => task.dataFetchStatus === 'failed').length
    };
    console.log('任务列表按状态统计:', tasksByStatus);

    // 3. 对比结果
    console.log('\n🔍 对比统计数据与任务列表数据...');
    console.log(`待执行任务: 统计=${stats.pending}, 列表=${tasksByStatus.pending}, 匹配=${stats.pending === tasksByStatus.pending ? '✅' : '❌'}`);
    console.log(`拉取中任务: 统计=${stats.fetching}, 列表=${tasksByStatus.fetching}, 匹配=${stats.fetching === tasksByStatus.fetching ? '✅' : '❌'}`);
    console.log(`成功任务: 统计=${stats.success}, 列表=${tasksByStatus.success}, 匹配=${stats.success === tasksByStatus.success ? '✅' : '❌'}`);
    console.log(`失败任务: 统计=${stats.failed}, 列表=${tasksByStatus.failed}, 匹配=${stats.failed === tasksByStatus.failed ? '✅' : '❌'}`);

    // 4. 测试前端计算逻辑
    console.log('\n🖥️ 模拟前端计算逻辑...');
    const frontendPendingCount = taskList.tasks.filter(task => task.dataFetchStatus === 'pending').length;
    console.log(`前端计算的待执行任务数: ${frontendPendingCount}`);
    console.log(`与统计数据匹配: ${stats.pending === frontendPendingCount ? '✅' : '❌'}`);

    console.log('\n🎉 测试完成!');
    
    return {
      statsAPI: stats,
      taskListAPI: taskList,
      comparison: {
        pending: stats.pending === tasksByStatus.pending,
        fetching: stats.fetching === tasksByStatus.fetching,
        success: stats.success === tasksByStatus.success,
        failed: stats.failed === tasksByStatus.failed
      },
      frontendMatch: stats.pending === frontendPendingCount
    };

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testAPIEndpoints()
    .then(result => {
      console.log('\n📋 测试结果摘要:');
      console.log('- 统计API正常:', result.statsAPI ? '✅' : '❌');
      console.log('- 任务列表API正常:', result.taskListAPI ? '✅' : '❌');
      console.log('- 数据一致性:', Object.values(result.comparison).every(v => v) ? '✅' : '❌');
      console.log('- 前端计算匹配:', result.frontendMatch ? '✅' : '❌');
      
      const allPassed = result.statsAPI && result.taskListAPI && 
                       Object.values(result.comparison).every(v => v) && 
                       result.frontendMatch;
      
      process.exit(allPassed ? 0 : 1);
    })
    .catch(error => {
      console.error('测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = testAPIEndpoints;
