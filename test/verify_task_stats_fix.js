/**
 * 验证任务统计修复的测试脚本
 * 检查统计数据与任务列表数据的一致性
 */

const { CooperationManagement, sequelize } = require('../src/models');
const cooperationService = require('../src/services/CooperationService');
const { Op } = require('sequelize');

class TaskStatsVerifier {
  constructor() {
    this.cooperationService = cooperationService;
    this.results = [];
  }

  /**
   * 添加测试结果
   */
  addResult(testName, success, message) {
    this.results.push({
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    });

    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * 验证统计数据与任务列表数据的一致性
   */
  async verifyStatsConsistency() {
    console.log('🔍 开始验证统计数据与任务列表数据的一致性...\n');

    try {
      // 1. 获取统计数据
      console.log('📊 获取统计数据...');
      const stats = await this.cooperationService.getDataFetchTaskStats();
      console.log('统计数据:', stats);

      // 2. 获取任务列表数据（不分页，获取所有数据）
      console.log('\n📋 获取任务列表数据...');
      const taskListResult = await this.cooperationService.getDataFetchTasks({
        page: 1,
        limit: 10000 // 获取足够多的数据
      });

      const allTasks = taskListResult.tasks;
      console.log(`任务列表总数: ${allTasks.length}`);

      // 3. 按状态分组统计任务列表中的数据
      const tasksByStatus = {
        pending: allTasks.filter(task => task.dataFetchStatus === 'pending').length,
        fetching: allTasks.filter(task => task.dataFetchStatus === 'fetching').length,
        success: allTasks.filter(task => task.dataFetchStatus === 'success').length,
        failed: allTasks.filter(task => task.dataFetchStatus === 'failed').length,
        total: allTasks.length
      };

      console.log('任务列表按状态统计:', tasksByStatus);

      // 4. 对比统计数据与任务列表数据
      console.log('\n🔍 对比统计数据与任务列表数据...');

      // 验证总数
      const totalMatch = stats.total === tasksByStatus.total;
      this.addResult('total_count_match', totalMatch, `总数匹配: 统计${stats.total} vs 列表${tasksByStatus.total}`);

      // 验证待执行数量
      const pendingMatch = stats.pending === tasksByStatus.pending;
      this.addResult(
        'pending_count_match',
        pendingMatch,
        `待执行数量匹配: 统计${stats.pending} vs 列表${tasksByStatus.pending}`
      );

      // 验证拉取中数量
      const fetchingMatch = stats.fetching === tasksByStatus.fetching;
      this.addResult(
        'fetching_count_match',
        fetchingMatch,
        `拉取中数量匹配: 统计${stats.fetching} vs 列表${tasksByStatus.fetching}`
      );

      // 验证成功数量
      const successMatch = stats.success === tasksByStatus.success;
      this.addResult(
        'success_count_match',
        successMatch,
        `成功数量匹配: 统计${stats.success} vs 列表${tasksByStatus.success}`
      );

      // 验证失败数量
      const failedMatch = stats.failed === tasksByStatus.failed;
      this.addResult(
        'failed_count_match',
        failedMatch,
        `失败数量匹配: 统计${stats.failed} vs 列表${tasksByStatus.failed}`
      );

      // 5. 检查是否存在dataRegistrationDate为空但dataFetchStatus为pending的记录
      console.log('\n🔍 检查数据完整性...');
      const pendingWithoutRegDate = await CooperationManagement.count({
        where: {
          dataFetchStatus: 'pending',
          dataRegistrationDate: null
        }
      });

      this.addResult(
        'data_integrity_check',
        pendingWithoutRegDate === 0,
        `待执行但无登记日期的记录数: ${pendingWithoutRegDate}`
      );

      // 6. 验证查询条件一致性
      const baseWhereCondition = {
        dataRegistrationDate: {
          [Op.ne]: null
        }
      };

      const totalWithCondition = await CooperationManagement.count({
        where: baseWhereCondition
      });

      const conditionMatch = stats.total === totalWithCondition;
      this.addResult(
        'query_condition_consistency',
        conditionMatch,
        `查询条件一致性: 统计${stats.total} vs 条件查询${totalWithCondition}`
      );
    } catch (error) {
      this.addResult('verification_process', false, `验证过程出错: ${error.message}`);
      console.error('验证过程出错:', error);
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log('\n📋 测试报告');
    console.log('='.repeat(50));

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => !r.success)
        .forEach(result => {
          console.log(`  - ${result.test}: ${result.message}`);
        });
    }

    console.log('\n' + '='.repeat(50));

    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: ((passedTests / totalTests) * 100).toFixed(1)
    };
  }

  /**
   * 运行完整验证
   */
  async run() {
    try {
      await this.verifyStatsConsistency();
      return this.generateReport();
    } catch (error) {
      console.error('验证脚本运行失败:', error);
      throw error;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const verifier = new TaskStatsVerifier();

  verifier
    .run()
    .then(report => {
      console.log('\n🎉 验证完成!');
      process.exit(report.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('验证失败:', error);
      process.exit(1);
    });
}

module.exports = TaskStatsVerifier;
