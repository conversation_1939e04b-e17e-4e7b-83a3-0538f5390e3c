{"基础配置示例": {"description": "最简单的配置，适合快速开始", "config": {"searchType": 1, "pageSize": 20, "retries": 3}}, "高级配置示例": {"description": "包含延迟、过滤和视频保存的完整配置", "config": {"searchType": 1, "pageSize": 20, "retries": 3, "delay": {"min": 1000, "max": 3000}, "filters": {"minFollowers": 1000, "maxFollowers": 1000000}, "saveVideos": true}}, "小红书美妆达人配置": {"description": "专门用于小红书美妆达人搜索的配置", "config": {"searchType": 1, "pageSize": 20, "retries": 3, "minFirstNotePlayCount": 10000, "delay": {"min": 2000, "max": 5000}, "filters": {"minFollowers": 5000, "location": ["北京", "上海", "广州", "深圳", "杭州"], "tags": ["美妆", "护肤", "彩妆"]}, "saveVideos": true, "qualityFilter": {"minEngagementRate": 0.03, "minVideoQuality": "720p"}}}, "巨量星图电商配置": {"description": "适用于巨量星图电商类达人搜索", "config": {"searchType": 1, "pageSize": 15, "retries": 3, "delay": {"min": 1000, "max": 3000}, "filters": {"minFollowers": 10000, "platform": "do<PERSON><PERSON>", "categories": ["电商", "好物推荐", "种草"]}, "saveVideos": false, "commercialFilter": {"hasShop": true, "minSalesVolume": 1000}}}, "高质量内容过滤配置": {"description": "严格的质量过滤，适合寻找高质量创作者", "config": {"searchType": 1, "pageSize": 10, "retries": 5, "minFirstNotePlayCount": 50000, "delay": {"min": 3000, "max": 6000}, "filters": {"minFollowers": 50000, "maxFollowers": 5000000, "location": ["一线城市"], "verified": true}, "qualityMetrics": {"minEngagementRate": 0.05, "minContentQuality": 8.0, "minPostFrequency": 3, "maxInactivityDays": 7}, "saveVideos": true, "enableDeepAnalysis": true}}, "批量采集配置": {"description": "适合大批量数据采集的高效配置", "config": {"searchType": 1, "pageSize": 50, "retries": 2, "delay": {"min": 500, "max": 1500}, "filters": {"minFollowers": 1000}, "saveVideos": false, "batchMode": {"enabled": true, "batchSize": 100, "parallelRequests": 3}, "dataOptimization": {"compressData": true, "skipDuplicates": true, "cacheResults": true}}}, "地域定向配置": {"description": "针对特定地区的达人搜索配置", "config": {"searchType": 1, "pageSize": 20, "retries": 3, "delay": {"min": 1500, "max": 3500}, "filters": {"minFollowers": 2000, "location": ["北京", "上海"], "locationPriority": ["朝阳区", "浦东新区", "海淀区", "黄浦区"]}, "geoTargeting": {"radius": 50, "coordinates": {"beijing": {"lat": 39.9042, "lng": 116.4074}, "shanghai": {"lat": 31.2304, "lng": 121.4737}}}, "saveVideos": true}}, "多平台联合配置": {"description": "跨平台数据采集的综合配置", "config": {"searchType": 1, "pageSize": 20, "retries": 3, "delay": {"min": 2000, "max": 4000}, "platforms": {"xiaohongshu": {"minFirstNotePlayCount": 5000, "filters": {"minFollowers": 3000, "categories": ["生活", "美妆", "穿搭"]}}, "douyin": {"filters": {"minFollowers": 10000, "categories": ["娱乐", "教育", "科技"]}}}, "crossPlatformAnalysis": {"enabled": true, "matchingFields": ["nickname", "avatarUrl"], "similarityThreshold": 0.8}, "saveVideos": true}}, "实时监控配置": {"description": "用于实时监控和数据更新的配置", "config": {"searchType": 1, "pageSize": 30, "retries": 3, "delay": {"min": 1000, "max": 2000}, "filters": {"minFollowers": 1000, "lastActiveHours": 24}, "realTimeMode": {"enabled": true, "updateInterval": 3600, "alertThresholds": {"followerGrowth": 1000, "engagementDrop": 0.5}}, "notifications": {"email": true, "webhook": "https://your-webhook-url.com/notify"}, "saveVideos": false}}, "AI增强配置": {"description": "使用AI分析增强数据质量的配置", "config": {"searchType": 1, "pageSize": 15, "retries": 3, "delay": {"min": 2000, "max": 4000}, "filters": {"minFollowers": 5000}, "aiAnalysis": {"enabled": true, "contentAnalysis": {"sentiment": true, "topics": true, "quality": true}, "imageAnalysis": {"faceDetection": true, "objectRecognition": true, "aestheticScore": true}, "textAnalysis": {"languageDetection": true, "keywordExtraction": true, "readabilityScore": true}}, "saveVideos": true, "exportFormat": {"includeAIScores": true, "detailedAnalysis": true}}}, "测试环境配置": {"description": "用于开发和测试的安全配置", "config": {"searchType": 1, "pageSize": 5, "retries": 1, "delay": {"min": 5000, "max": 10000}, "filters": {"minFollowers": 100}, "testMode": {"enabled": true, "maxResults": 50, "dryRun": false, "debugLevel": "verbose"}, "saveVideos": false, "mockData": {"enabled": false, "dataSet": "sample_influencers.json"}}}}