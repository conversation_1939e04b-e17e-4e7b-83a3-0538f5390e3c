/**
 * 巨量星图爬虫首个帖子播放量过滤功能使用示例
 * 展示如何使用新增的 minFirstNotePlayCount 参数进行高质量达人筛选
 */

const XingtuCrawler = require('../src/services/crawler/crawlers/XingtuCrawler');

/**
 * 示例1: 基础使用 - 设置播放量阈值
 */
async function basicFilterExample() {
  console.log('📋 示例1: 基础播放量过滤\n');

  const crawler = new XingtuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '美妆',
    maxPages: 2,
    pageSize: 10,
    minFirstNotePlayCount: 5000, // 首个帖子最低播放量5000
    saveVideos: false
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`进度: ${progress.percentage}% (成功:${progress.successCount}, 失败:${progress.failedCount})`);
    },
    onResult: async (result) => {
      console.log(`✅ 获取达人: ${result.nickname} - 粉丝:${result.followersCount}`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`\n结果: 总计${results.totalCount}, 成功${results.successCount}, 失败${results.failedCount}\n`);
}

/**
 * 示例2: 高质量过滤 - 设置较高阈值
 */
async function highQualityFilterExample() {
  console.log('📋 示例2: 高质量内容过滤\n');

  const crawler = new XingtuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '护肤',
    maxPages: 1,
    pageSize: 20,
    minFirstNotePlayCount: 20000, // 首个帖子最低播放量2万
    saveVideos: false
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`高质量过滤进度: ${progress.percentage}%`);
    },
    onResult: async (result) => {
      console.log(`🌟 高质量达人: ${result.nickname}`);
      console.log(`   粉丝数: ${result.followersCount}`);
      console.log(`   城市: ${result.city}`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`\n高质量过滤结果: ${results.successCount}/${results.totalCount} 达人通过筛选\n`);
}

/**
 * 示例3: 无过滤模式 - 阈值设为0
 */
async function noFilterExample() {
  console.log('📋 示例3: 无过滤模式\n');

  const crawler = new XingtuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '时尚',
    maxPages: 1,
    pageSize: 5,
    minFirstNotePlayCount: 0, // 不进行播放量过滤
    saveVideos: false
  };

  const callbacks = {
    onResult: async (result) => {
      console.log(`📝 达人: ${result.nickname} - 播放量中位数: ${result.playMid || '无'}`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`\n无过滤结果: ${results.successCount}/${results.totalCount} 达人获取成功\n`);
}

/**
 * 示例4: 单独测试过滤方法
 */
async function testFilterMethodExample() {
  console.log('📋 示例4: 单独测试过滤方法\n');

  const crawler = new XingtuCrawler();
  await crawler.initialize();

  // 测试达人ID（需要替换为实际的达人ID）
  const testAuthorId = 'test_author_id';
  const minPlayCount = 10000;

  try {
    console.log(`🔍 测试达人 ${testAuthorId} 的首个帖子播放量过滤...`);
    
    const result = await crawler.checkFirstNotePlayCount(testAuthorId, minPlayCount);
    
    console.log('📊 过滤结果:');
    console.log(`   是否通过: ${result.passed ? '✅ 是' : '❌ 否'}`);
    console.log(`   首个帖子播放量: ${result.firstNotePlayCount}`);
    console.log(`   帖子ID: ${result.noteId || '无'}`);
    console.log(`   原因: ${result.reason}`);
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
  }
  console.log('');
}

/**
 * 示例5: 完整的爬取流程配置
 */
async function fullConfigExample() {
  console.log('📋 示例5: 完整配置示例\n');

  const crawler = new XingtuCrawler();
  await crawler.initialize();

  const config = {
    // 基础搜索配置
    keywords: '美食',
    maxPages: 3,
    pageSize: 15,
    
    // 新增：首个帖子播放量过滤
    minFirstNotePlayCount: 8000,
    
    // 其他配置
    saveVideos: true,
    crawlTaskId: 'example_task_001'
  };

  const callbacks = {
    onProgress: async (progress) => {
      console.log(`📈 进度更新: 第${progress.currentPage}/${progress.totalPages}页 (${progress.percentage}%)`);
      console.log(`   成功: ${progress.successCount}, 失败: ${progress.failedCount}`);
    },
    onResult: async (result) => {
      console.log(`🎯 符合条件的达人: ${result.nickname}`);
      console.log(`   平台用户ID: ${result.platformUserId}`);
      console.log(`   粉丝数: ${result.followersCount}`);
      console.log(`   播放量中位数: ${result.playMid || '无'}`);
      console.log(`   视频统计: ${result.videoStats?.videoCount || 0} 个视频`);
    },
    onError: async (error) => {
      console.log(`⚠️ 处理错误: ${error.message}`);
    }
  };

  try {
    const results = await crawler.crawl(config, callbacks);
    
    console.log('\n🎉 爬取完成！');
    console.log('📊 最终统计:');
    console.log(`   总计处理: ${results.totalCount} 个达人`);
    console.log(`   成功获取: ${results.successCount} 个达人`);
    console.log(`   过滤失败: ${results.failedCount} 个达人`);
    console.log(`   成功率: ${((results.successCount / results.totalCount) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.log(`❌ 爬取失败: ${error.message}`);
  }
  console.log('');
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 巨量星图首个帖子播放量过滤功能示例\n');
  console.log('=' .repeat(60));
  
  try {
    // 运行所有示例（实际使用时可以选择性运行）
    
    // await basicFilterExample();
    // await highQualityFilterExample();
    // await noFilterExample();
    // await testFilterMethodExample();
    // await fullConfigExample();
    
    // 显示配置建议
    console.log('📋 配置建议:\n');
    
    const suggestions = [
      { threshold: 1000, description: '低质量过滤（适用于初步筛选）' },
      { threshold: 5000, description: '中等质量过滤（适用于一般需求）' },
      { threshold: 10000, description: '高质量过滤（适用于精品内容）' },
      { threshold: 50000, description: '顶级质量过滤（适用于头部达人）' },
      { threshold: 0, description: '不进行过滤（获取所有达人）' }
    ];
    
    suggestions.forEach(item => {
      console.log(`   minFirstNotePlayCount: ${item.threshold.toLocaleString()} - ${item.description}`);
    });
    
    console.log('\n🔧 使用提示:');
    console.log('   1. 根据业务需求设置合适的播放量阈值');
    console.log('   2. 较高的阈值会过滤掉更多达人，但质量更高');
    console.log('   3. 设置为0可以禁用过滤功能');
    console.log('   4. 过滤失败的达人会计入failedCount统计');
    console.log('   5. 系统会自动使用备用数据进行兜底处理');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error.message);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runAllExamples()
    .then(() => {
      console.log('\n✅ 示例运行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 示例运行异常:', error);
      process.exit(1);
    });
}

module.exports = {
  basicFilterExample,
  highQualityFilterExample,
  noFilterExample,
  testFilterMethodExample,
  fullConfigExample,
  runAllExamples
};
