/**
 * 爬虫配置验证测试脚本
 * 
 * 功能说明：
 * - 测试新的配置验证机制
 * - 验证各种配置格式的处理
 * - 检查错误提示的准确性
 * - 确保配置传递的完整性
 * 
 * 使用方法：
 * node scripts/test_config_validation.js
 */

const path = require('path');

// 设置项目根目录
process.chdir(path.join(__dirname, '..'));

// 导入控制器
const CrawlerController = require('../src/controllers/CrawlerController');

/**
 * 测试用例定义
 */
const testCases = [
  {
    name: '空配置测试',
    config: null,
    expectSuccess: true,
    expectedResult: {}
  },
  {
    name: '空字符串配置测试',
    config: '',
    expectSuccess: true,
    expectedResult: {}
  },
  {
    name: '基础JSON对象测试',
    config: { searchType: 1, pageSize: 20 },
    expectSuccess: true,
    expectedResult: { searchType: 1, pageSize: 20 }
  },
  {
    name: 'JSON字符串测试',
    config: '{"searchType": 1, "pageSize": 20}',
    expectSuccess: true,
    expectedResult: { searchType: 1, pageSize: 20 }
  },
  {
    name: '复杂嵌套对象测试',
    config: {
      searchType: 1,
      delay: { min: 1000, max: 3000 },
      filters: {
        demographics: { age: { min: 18, max: 35 } },
        engagement: { minLikes: 1000 }
      }
    },
    expectSuccess: true
  },
  {
    name: '数组配置测试',
    config: {
      keywords: ['美妆', '护肤'],
      location: ['北京', '上海']
    },
    expectSuccess: true
  },
  {
    name: '无效JSON字符串测试',
    config: '{"searchType": 1, "pageSize":}',
    expectSuccess: false,
    expectedError: 'JSON_PARSE_ERROR'
  },
  {
    name: '数组根对象测试',
    config: [1, 2, 3],
    expectSuccess: false,
    expectedError: 'STRUCTURE_ERROR'
  },
  {
    name: '基本类型配置测试',
    config: 123,
    expectSuccess: false,
    expectedError: 'TYPE_ERROR'
  },
  {
    name: '字段类型错误测试',
    config: { pageSize: 'invalid' },
    expectSuccess: false,
    expectedError: 'FIELD_TYPE_ERROR'
  },
  {
    name: '字段范围错误测试',
    config: { pageSize: 200 },
    expectSuccess: false,
    expectedError: 'FIELD_RANGE_ERROR'
  },
  {
    name: '过深嵌套测试',
    config: createDeepNestedObject(15),
    expectSuccess: false,
    expectedError: 'DEPTH_ERROR'
  }
];

/**
 * 创建深度嵌套对象
 */
function createDeepNestedObject(depth) {
  let obj = { value: 'deep' };
  for (let i = 0; i < depth; i++) {
    obj = { nested: obj };
  }
  return obj;
}

/**
 * 运行单个测试用例
 */
async function runTestCase(testCase) {
  console.log(`\n🧪 测试: ${testCase.name}`);
  console.log(`   输入配置:`, typeof testCase.config === 'string' ? 
    testCase.config : JSON.stringify(testCase.config));

  try {
    const result = CrawlerController.validateAndProcessConfig(testCase.config);
    
    if (testCase.expectSuccess) {
      console.log(`   ✅ 测试通过 - 配置验证成功`);
      console.log(`   结果:`, JSON.stringify(result, null, 2));
      
      if (testCase.expectedResult) {
        const matches = JSON.stringify(result) === JSON.stringify(testCase.expectedResult);
        if (matches) {
          console.log(`   ✅ 结果匹配预期`);
        } else {
          console.log(`   ❌ 结果不匹配预期`);
          console.log(`   预期:`, JSON.stringify(testCase.expectedResult, null, 2));
        }
      }
    } else {
      console.log(`   ❌ 测试失败 - 预期应该抛出错误，但验证成功了`);
      return false;
    }
  } catch (error) {
    if (!testCase.expectSuccess) {
      console.log(`   ✅ 测试通过 - 正确抛出错误`);
      console.log(`   错误类型:`, error.name);
      console.log(`   错误信息:`, error.message);
      
      if (testCase.expectedError && error.details) {
        const errorTypeMatches = error.details.type === testCase.expectedError;
        if (errorTypeMatches) {
          console.log(`   ✅ 错误类型匹配预期`);
        } else {
          console.log(`   ❌ 错误类型不匹配预期`);
          console.log(`   预期错误类型:`, testCase.expectedError);
          console.log(`   实际错误类型:`, error.details.type);
        }
      }
      
      if (error.details) {
        console.log(`   错误详情:`, JSON.stringify(error.details, null, 2));
      }
    } else {
      console.log(`   ❌ 测试失败 - 预期应该验证成功，但抛出了错误`);
      console.log(`   错误:`, error.message);
      return false;
    }
  }
  
  return true;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始爬虫配置验证测试\n');
  console.log('=' .repeat(60));
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    const passed = await runTestCase(testCase);
    if (passed) {
      passedTests++;
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`📊 测试结果统计:`);
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   通过测试: ${passedTests}`);
  console.log(`   失败测试: ${totalTests - passedTests}`);
  console.log(`   通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log(`\n🎉 所有测试通过！配置验证机制工作正常。`);
  } else {
    console.log(`\n⚠️  有 ${totalTests - passedTests} 个测试失败，请检查配置验证逻辑。`);
  }
}

/**
 * 测试配置传递机制
 */
async function testConfigPassing() {
  console.log('\n🔄 测试配置传递机制\n');
  console.log('-'.repeat(40));
  
  const testConfigs = [
    {
      name: '基础配置传递',
      config: { searchType: 1, pageSize: 30, customField: 'test' }
    },
    {
      name: '复杂嵌套配置传递',
      config: {
        searchType: 1,
        customSettings: {
          advanced: {
            filters: ['filter1', 'filter2'],
            options: { enable: true, level: 5 }
          }
        }
      }
    }
  ];
  
  for (const test of testConfigs) {
    console.log(`\n📋 ${test.name}:`);
    console.log(`   原始配置:`, JSON.stringify(test.config, null, 2));
    
    try {
      const processed = CrawlerController.validateAndProcessConfig(test.config);
      console.log(`   处理后配置:`, JSON.stringify(processed, null, 2));
      
      // 检查配置是否完整保留
      const originalKeys = Object.keys(test.config);
      const processedKeys = Object.keys(processed);
      const allKeysPreserved = originalKeys.every(key => processedKeys.includes(key));
      
      if (allKeysPreserved) {
        console.log(`   ✅ 配置字段完整保留`);
      } else {
        console.log(`   ❌ 配置字段丢失`);
        console.log(`   原始字段:`, originalKeys);
        console.log(`   处理后字段:`, processedKeys);
      }
    } catch (error) {
      console.log(`   ❌ 配置处理失败:`, error.message);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await runAllTests();
    await testConfigPassing();
    
    console.log('\n✨ 测试完成！');
  } catch (error) {
    console.error('\n💥 测试执行失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  runAllTests,
  testConfigPassing,
  testCases
};
