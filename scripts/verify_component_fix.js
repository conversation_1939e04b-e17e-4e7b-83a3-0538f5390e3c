/**
 * 验证 CrawlerTaskModal 组件修复效果
 * 
 * 这个脚本验证组件初始化时不会出现 "Cannot access 'resetForm' before initialization" 错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 CrawlerTaskModal 组件修复效果...\n');

// 读取组件文件
const componentPath = path.join(__dirname, '../frontend/src/components/CrawlerTaskModal.vue');
const componentContent = fs.readFileSync(componentPath, 'utf8');

// 检查关键代码结构
const checks = [
  {
    name: 'resetForm 函数定义',
    pattern: /const resetForm = \(\) => \{/,
    description: '检查 resetForm 函数是否正确定义'
  },
  {
    name: 'watch 监听器',
    pattern: /watch\(\s*\(\) => props\.editingTask/,
    description: '检查 watch 监听器是否正确设置'
  },
  {
    name: 'resetForm 调用',
    pattern: /resetForm\(\);/,
    description: '检查 resetForm 函数是否被正确调用'
  },
  {
    name: 'loadTaskData 函数',
    pattern: /const loadTaskData = /,
    description: '检查 loadTaskData 函数是否存在'
  }
];

let allPassed = true;

checks.forEach((check, index) => {
  const found = check.pattern.test(componentContent);
  const status = found ? '✅' : '❌';
  console.log(`${index + 1}. ${status} ${check.name}`);
  console.log(`   ${check.description}`);
  
  if (!found) {
    allPassed = false;
    console.log(`   ⚠️  未找到匹配的代码模式`);
  }
  console.log('');
});

// 检查函数定义顺序
console.log('📋 检查函数定义顺序...\n');

const resetFormIndex = componentContent.indexOf('const resetForm = ');
const watchIndex = componentContent.indexOf('watch(');
const loadTaskDataIndex = componentContent.indexOf('const loadTaskData = ');

if (resetFormIndex !== -1 && watchIndex !== -1) {
  if (resetFormIndex < watchIndex) {
    console.log('✅ resetForm 函数在 watch 之前定义 - 正确');
  } else {
    console.log('❌ resetForm 函数在 watch 之后定义 - 可能导致初始化错误');
    allPassed = false;
  }
} else {
  console.log('❌ 无法找到 resetForm 或 watch 定义');
  allPassed = false;
}

if (loadTaskDataIndex !== -1 && watchIndex !== -1) {
  if (loadTaskDataIndex < watchIndex) {
    console.log('✅ loadTaskData 函数在 watch 之前定义 - 正确');
  } else {
    console.log('❌ loadTaskData 函数在 watch 之后定义 - 可能导致初始化错误');
    allPassed = false;
  }
} else {
  console.log('❌ 无法找到 loadTaskData 或 watch 定义');
  allPassed = false;
}

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 所有检查通过！组件修复成功。');
  console.log('\n✅ 修复内容：');
  console.log('   • 将 resetForm 函数移到 watch 之前定义');
  console.log('   • 删除重复的函数定义');
  console.log('   • 确保函数调用顺序正确');
  console.log('\n🚀 组件现在应该能够正常初始化，不会出现引用错误。');
} else {
  console.log('❌ 检查失败！仍存在问题需要修复。');
  process.exit(1);
}

// 额外的代码质量检查
console.log('\n🔧 代码质量检查...\n');

const qualityChecks = [
  {
    name: '重复函数定义',
    pattern: /(const resetForm = .*?\{[\s\S]*?\};)[\s\S]*(const resetForm = .*?\{[\s\S]*?\};)/,
    shouldNotExist: true,
    description: '检查是否存在重复的 resetForm 函数定义'
  },
  {
    name: 'Props 定义',
    pattern: /const props = defineProps\(/,
    shouldNotExist: false,
    description: '检查 props 是否正确定义'
  },
  {
    name: 'Emits 定义',
    pattern: /const emit = defineEmits\(/,
    shouldNotExist: false,
    description: '检查 emits 是否正确定义'
  }
];

qualityChecks.forEach((check, index) => {
  const found = check.pattern.test(componentContent);
  const passed = check.shouldNotExist ? !found : found;
  const status = passed ? '✅' : '❌';
  
  console.log(`${index + 1}. ${status} ${check.name}`);
  console.log(`   ${check.description}`);
  
  if (!passed) {
    if (check.shouldNotExist) {
      console.log(`   ⚠️  发现不应该存在的代码模式`);
    } else {
      console.log(`   ⚠️  未找到必需的代码模式`);
    }
  }
  console.log('');
});

console.log('✨ 验证完成！');
