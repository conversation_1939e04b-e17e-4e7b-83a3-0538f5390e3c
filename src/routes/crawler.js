/**
 * 爬虫相关路由
 */

const Router = require('koa-router');
const CrawlerController = require('../controllers/CrawlerController');
const { authenticate } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/crawler'
});

// 所有爬虫接口都需要认证
router.use(authenticate);

// 任务管理
router.post('/tasks', CrawlerController.createTask.bind(CrawlerController)); // 创建任务
router.put('/tasks/:id', CrawlerController.updateTask.bind(CrawlerController)); // 更新任务
router.get('/tasks', CrawlerController.getTasks.bind(CrawlerController)); // 获取任务列表
router.get('/tasks/:id', CrawlerController.getTaskDetail.bind(CrawlerController)); // 获取任务详情
/**
 * @swagger
 * /crawler/tasks/{id}:
 *   put:
 *     summary: 更新爬虫任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               taskName:
 *                 type: string
 *                 description: 任务名称
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, douyin, kuaishou]
 *                 description: 平台类型
 *               keywords:
 *                 type: string
 *                 description: 搜索关键词
 *               maxPages:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 description: 最大页数
 *               priority:
 *                 type: integer
 *                 enum: [0, 1, 2, 3]
 *                 description: 任务优先级（0-低，1-普通，2-高，3-紧急）
 *               config:
 *                 type: object
 *                 description: 爬取配置参数
 *                 properties:
 *                   minFirstNotePlayCount:
 *                     type: integer
 *                     minimum: 0
 *                     description: 首个帖子最低播放量
 *                   pageSize:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 100
 *                     description: 每页数量
 *                   saveVideos:
 *                     type: boolean
 *                     description: 是否保存视频
 *                   retries:
 *                     type: integer
 *                     minimum: 0
 *                     maximum: 10
 *                     description: 重试次数
 *     responses:
 *       200:
 *         description: 任务更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */

/**
 * @swagger
 * /crawler/tasks/{id}/start:
 *   post:
 *     summary: 启动爬虫任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务启动成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post('/tasks/:id/start', CrawlerController.startTask.bind(CrawlerController)); // 启动任务

/**
 * @swagger
 * /crawler/tasks/{id}/stop:
 *   post:
 *     summary: 停止爬虫任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务停止成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post('/tasks/:id/stop', CrawlerController.stopTask.bind(CrawlerController)); // 停止任务（暂停）

/**
 * @swagger
 * /crawler/tasks/{id}/force-stop:
 *   post:
 *     summary: 强制停止爬虫任务（用于处理状态异常）
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务强制停止成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post('/tasks/:id/force-stop', CrawlerController.forceStopTask.bind(CrawlerController)); // 强制停止任务

/**
 * @swagger
 * /crawler/tasks/{id}/diagnose:
 *   get:
 *     summary: 诊断爬虫任务状态（调试用）
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 诊断完成
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get('/tasks/:id/diagnose', CrawlerController.diagnoseTask); // 诊断任务状态

/**
 * @swagger
 * /crawler/tasks/check-inconsistent:
 *   post:
 *     summary: 检查和修复所有状态不一致的任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 检查完成
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.post('/tasks/check-inconsistent', CrawlerController.checkInconsistentTasks.bind(CrawlerController)); // 检查状态不一致任务

/**
 * @swagger
 * /crawler/tasks/{id}/resume:
 *   post:
 *     summary: 恢复暂停的爬虫任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务恢复成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post('/tasks/:id/resume', CrawlerController.resumeTask.bind(CrawlerController)); // 恢复任务

/**
 * @swagger
 * /crawler/tasks/{id}/retry:
 *   post:
 *     summary: 重试失败的爬虫任务
 *     description: 重新启动失败的爬虫任务，重置任务状态并重新加入队列
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 任务重试成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CrawlTask'
 *             example:
 *               success: true
 *               message: "任务重试成功"
 *               data:
 *                 id: 1
 *                 taskName: "美食达人爬取"
 *                 status: "pending"
 *                 retryCount: 1
 *                 lastRetryAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: 重试失败（任务状态不正确或达到最大重试次数）
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               not_failed:
 *                 summary: 任务状态不正确
 *                 value:
 *                   success: false
 *                   message: "只有失败的任务才能重试"
 *               max_retries:
 *                 summary: 达到最大重试次数
 *                 value:
 *                   success: false
 *                   message: "任务已达到最大重试次数 (3)"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 */
router.post('/tasks/:id/retry', CrawlerController.retryTask.bind(CrawlerController)); // 重试任务

router.delete('/tasks/:id', CrawlerController.deleteTask.bind(CrawlerController)); // 删除任务
router.post('/tasks/batch-delete', CrawlerController.batchDeleteTasks.bind(CrawlerController)); // 批量删除任务

/**
 * @swagger
 * /crawler/tasks/batch-retry:
 *   post:
 *     summary: 批量重试失败的爬虫任务
 *     description: 批量重新启动多个失败的爬虫任务
 *     tags: [爬虫管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 要重试的任务ID数组
 *                 example: [1, 2, 3]
 *     responses:
 *       200:
 *         description: 批量重试完成
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         successCount:
 *                           type: integer
 *                           description: 成功重试的任务数量
 *                         errors:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               taskId:
 *                                 type: integer
 *                                 description: 失败的任务ID
 *                               error:
 *                                 type: string
 *                                 description: 错误信息
 *             example:
 *               success: true
 *               message: "成功重试 2 个任务"
 *               data:
 *                 successCount: 2
 *                 errors: [
 *                   {
 *                     taskId: 3,
 *                     error: "任务已达到最大重试次数 (3)"
 *                   }
 *                 ]
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.post('/tasks/batch-retry', CrawlerController.batchRetryTasks); // 批量重试任务

// 任务结果和日志
router.get('/tasks/:id/results', CrawlerController.getTaskResults.bind(CrawlerController)); // 获取任务结果
router.get('/tasks/:id/logs', CrawlerController.getTaskLogs.bind(CrawlerController)); // 获取任务日志
router.post('/tasks/:id/import', CrawlerController.importTaskResults.bind(CrawlerController)); // 导入爬虫任务结果到达人表

// 爬虫结果管理
router.get('/results/:id', CrawlerController.getResultDetail.bind(CrawlerController)); // 获取结果详情
router.post('/results/batch-import', CrawlerController.batchImportResults.bind(CrawlerController)); // 批量导入结果
router.get('/results/export', CrawlerController.exportResults.bind(CrawlerController)); // 导出结果

// 服务状态和统计
router.get('/status', CrawlerController.getServiceStatus.bind(CrawlerController)); // 获取服务状态
router.get('/stats', CrawlerController.getStats.bind(CrawlerController)); // 获取统计信息

module.exports = router;
