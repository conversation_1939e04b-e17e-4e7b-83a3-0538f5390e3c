/**
 * 合作对接定时任务服务
 *
 * 功能说明：
 * - 管理合作对接相关的定时任务
 * - 每日上午9点检查需要拉取笔记数据的合作记录
 * - 自动调用小红书API获取笔记数据
 * - 更新数据库中的数据指标
 *
 * 定时任务：
 * - 每日9:00执行笔记数据拉取
 * - 检查到达定时拉取时间的记录
 * - 批量处理数据拉取请求
 * - 记录执行日志和错误信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const cron = require('node-cron');
const cooperationService = require('./CooperationService');
const CookieManager = require('./CookieManager');
const moment = require('moment');

class CooperationScheduleService {
  constructor() {
    this.isInitialized = false;
    this.scheduledTasks = new Map();
    this.cookieManager = new CookieManager();
  }

  /**
   * 初始化定时任务服务
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 启动每日笔记数据拉取
      this.startDailyDataFetchTask();

      this.isInitialized = true;
      console.log('✅ 合作对接定时任务服务初始化成功');
    } catch (error) {
      console.error('❌ 合作对接定时任务服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 启动每日笔记数据拉取
   * 每天上午9:00执行
   */
  startDailyDataFetchTask() {
    // 每天上午9:00执行
    const task = cron.schedule(
      '0 9 * * *',
      async () => {
        console.log('🕘 开始执行每日合作对接笔记数据拉取...');
        await this.executeDailyDataFetch();
      },
      {
        scheduled: true,
        timezone: 'Asia/Shanghai'
      }
    );

    this.scheduledTasks.set('dailyDataFetch', task);
    console.log('📅 每日笔记数据拉取已启动 (每天上午9:00执行)');
  }

  /**
   * 执行每日笔记数据拉取
   */
  async executeDailyDataFetch() {
    try {
      const startTime = moment();
      console.log(`🚀 开始执行笔记数据拉取: ${startTime.format('YYYY-MM-DD HH:mm:ss')}`);

      // 检查Cookie状态
      const cookieStatus = await this.checkCookieStatus();
      console.log(`🍪 Cookie状态检查: ${cookieStatus.message}`);

      if (!cookieStatus.hasAvailable) {
        console.warn('⚠️ 没有任何平台的可用Cookie，跳过笔记数据拉取');
        return;
      }

      // 显示各平台Cookie状态
      console.log('🍪 各平台Cookie状态:');
      for (const [platform, status] of Object.entries(cookieStatus.platforms)) {
        const platformName = platform === 'xiaohongshu' ? '小红书' : '巨量星图';
        const statusText = status.hasAvailable ? `✅ ${status.accountName}` : '❌ 无可用Cookie';
        console.log(`   ${platformName}: ${statusText}`);
      }

      // 获取需要拉取数据的记录
      const records = await cooperationService.getRecordsForDataFetch();

      if (records.length === 0) {
        console.log('📋 没有需要拉取数据的记录');
        return;
      }

      console.log(`📋 找到 ${records.length} 条需要拉取数据的记录`);

      let successCount = 0;
      let failureCount = 0;
      let cookieErrorCount = 0;
      const errors = [];
      const cookieUsageStats = new Map();

      // 批量处理数据拉取
      for (const record of records) {
        try {
          console.log(`🔄 正在拉取记录 ${record.id} 的数据 (博主: ${record.bloggerName})...`);
          const result = await cooperationService.fetchNoteData(record);

          if (result.success) {
            successCount++;
            console.log(`✅ 记录 ${record.id} 数据拉取成功`);

            // 统计Cookie使用情况
            if (result.cookieUsed) {
              const count = cookieUsageStats.get(result.cookieUsed) || 0;
              cookieUsageStats.set(result.cookieUsed, count + 1);
            }
          } else {
            failureCount++;

            // 检查是否是Cookie相关错误
            if (this.isCookieRelatedError(result.error)) {
              cookieErrorCount++;
            }

            errors.push({
              recordId: record.id,
              bloggerName: record.bloggerName,
              noteId: record.noteId,
              platform: record.platform,
              error: result.error
            });
            console.log(`❌ 记录 ${record.id} 数据拉取失败: ${result.error}`);
          }

          // 添加延迟，避免请求过于频繁
          await this.delay(2000);
        } catch (error) {
          failureCount++;
          errors.push({
            recordId: record.id,
            bloggerName: record.bloggerName,
            noteId: record.noteId,
            platform: record.platform,
            error: error.message
          });
          console.error(`❌ 记录 ${record.id} 处理异常:`, error.message);
        }
      }

      const endTime = new Date();
      const duration = Math.round((endTime - startTime) / 1000);

      // 输出执行结果统计
      console.log('📊 笔记数据拉取执行完成:');
      console.log(`   ⏱️  执行时间: ${duration}秒`);
      console.log(`   📝 处理记录: ${records.length}条`);
      console.log(`   ✅ 成功: ${successCount}条`);
      console.log(`   ❌ 失败: ${failureCount}条`);
      console.log(`   🍪 Cookie错误: ${cookieErrorCount}条`);

      // 输出Cookie使用统计
      if (cookieUsageStats.size > 0) {
        console.log('🍪 Cookie使用统计:');
        for (const [cookieName, count] of cookieUsageStats) {
          console.log(`   - ${cookieName}: ${count}次`);
        }
      }

      if (errors.length > 0) {
        console.log('❌ 失败记录详情:');
        errors.forEach(error => {
          console.log(
            `   - 记录ID: ${error.recordId}, 博主: ${error.bloggerName}, 平台: ${error.platform}, 笔记ID: ${error.noteId}, 错误: ${error.error}`
          );
        });
      }
    } catch (error) {
      console.error('❌ 执行每日笔记数据拉取失败:', error.message);
    }
  }

  /**
   * 手动触发笔记数据拉取
   * @returns {Promise<Object>} 执行结果
   */
  async triggerDataFetchTask() {
    try {
      console.log('🔄 手动触发笔记数据拉取...');
      await this.executeDailyDataFetch();
      return { success: true, message: '笔记数据拉取执行完成' };
    } catch (error) {
      console.error('❌ 手动触发笔记数据拉取失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取定时任务状态
   * @returns {Object} 任务状态信息
   */
  getTaskStatus() {
    const tasks = [];

    for (const [name, task] of this.scheduledTasks) {
      tasks.push({
        name,
        running: task.running,
        scheduled: task.scheduled,
        destroyed: task.destroyed
      });
    }

    return {
      initialized: this.isInitialized,
      tasks,
      totalTasks: this.scheduledTasks.size
    };
  }

  /**
   * 停止所有定时任务
   */
  stopAllTasks() {
    try {
      for (const [name, task] of this.scheduledTasks) {
        task.stop();
        console.log(`⏹️ 定时任务已停止: ${name}`);
      }
      console.log('⏹️ 所有合作对接定时任务已停止');
    } catch (error) {
      console.error('❌ 停止定时任务失败:', error.message);
    }
  }

  /**
   * 重启所有定时任务
   */
  restartAllTasks() {
    try {
      for (const [name, task] of this.scheduledTasks) {
        task.start();
        console.log(`🔄 定时任务已重启: ${name}`);
      }
      console.log('🔄 所有合作对接定时任务已重启');
    } catch (error) {
      console.error('❌ 重启定时任务失败:', error.message);
    }
  }

  /**
   * 销毁定时任务服务
   */
  destroy() {
    try {
      this.stopAllTasks();
      this.scheduledTasks.clear();
      this.isInitialized = false;
      console.log('🗑️ 合作对接定时任务服务已销毁');
    } catch (error) {
      console.error('❌ 销毁定时任务服务失败:', error.message);
    }
  }

  /**
   * 检查Cookie状态
   * @returns {Promise<Object>} Cookie状态信息
   */
  async checkCookieStatus() {
    try {
      const platforms = ['xiaohongshu', 'juxingtu'];
      const cookieStatus = {};
      let hasAnyAvailable = false;
      const messages = [];

      // 检查各平台的Cookie状态
      for (const platform of platforms) {
        const cookie = await this.cookieManager.getAvailableCookie(platform);

        if (cookie) {
          cookieStatus[platform] = {
            hasAvailable: true,
            accountName: cookie.accountName,
            priority: cookie.priority
          };
          hasAnyAvailable = true;

          const platformName = platform === 'xiaohongshu' ? '小红书' : '巨量星图';
          messages.push(`${platformName}平台有可用Cookie: ${cookie.accountName}`);
        } else {
          cookieStatus[platform] = {
            hasAvailable: false,
            accountName: null,
            priority: null
          };

          const platformName = platform === 'xiaohongshu' ? '小红书' : '巨量星图';
          messages.push(`${platformName}平台没有可用Cookie`);
        }
      }

      return {
        hasAvailable: hasAnyAvailable,
        message: messages.join('; '),
        cookieStatus,
        platforms: cookieStatus
      };
    } catch (error) {
      console.error('检查Cookie状态失败:', error.message);
      return {
        hasAvailable: false,
        message: `Cookie状态检查失败: ${error.message}`,
        cookieStatus: {},
        platforms: {}
      };
    }
  }

  /**
   * 检查是否是Cookie相关错误
   * @param {string} errorMessage 错误信息
   * @returns {boolean} 是否是Cookie相关错误
   */
  isCookieRelatedError(errorMessage) {
    const cookieErrorKeywords = ['cookie', '认证', '登录', '权限', 'auth', 'login', 'permission', '401', '403'];

    const lowerMessage = errorMessage.toLowerCase();
    return cookieErrorKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建单例实例
const cooperationScheduleService = new CooperationScheduleService();

module.exports = cooperationScheduleService;
