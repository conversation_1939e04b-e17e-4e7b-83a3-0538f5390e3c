/**
 * 任务队列管理器
 * 负责管理爬虫任务的队列和调度
 */

const { CrawlTask, PublicInfluencer, CrawlLog } = require('../../models');
const CookieManager = require('../CookieManager');

class TaskQueue {
  constructor() {
    this.queue = [];
    this.running = new Map(); // 正在运行的任务
    this.maxConcurrent = 3; // 最大并发数
    this.isProcessing = false;

    // 初始化依赖服务
    this.cookieManager = new CookieManager();
    this.crawlers = new Map(); // 缓存爬虫实例
  }

  /**
   * 初始化任务队列
   */
  async initialize() {
    console.log('📋 任务队列初始化...');

    // 初始化爬虫实例
    await this.initializeCrawlers();

    this.startProcessing();
  }

  /**
   * 初始化爬虫实例
   */
  async initializeCrawlers() {
    try {
      // 动态加载爬虫类
      const XiaohongshuCrawler = require('./crawlers/XiaohongshuCrawler');
      const XingtuCrawler = require('./crawlers/XingtuCrawler');

      // 创建爬虫实例
      const xiaohongshuCrawler = new XiaohongshuCrawler();
      const xingtuCrawler = new XingtuCrawler();

      // 初始化爬虫
      await xiaohongshuCrawler.initialize();
      await xingtuCrawler.initialize();

      // 缓存爬虫实例
      this.crawlers.set('xiaohongshu', xiaohongshuCrawler);
      this.crawlers.set('juxingtu', xingtuCrawler);

      console.log('✅ 爬虫实例初始化完成');
    } catch (error) {
      console.error('❌ 爬虫实例初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 添加任务到队列
   * @param {Object} task 任务对象
   */
  async addTask(task) {
    // 按优先级插入队列
    const insertIndex = this.queue.findIndex(queuedTask => queuedTask.priority < task.priority);

    if (insertIndex === -1) {
      this.queue.push(task);
    } else {
      this.queue.splice(insertIndex, 0, task);
    }

    console.log(`📋 任务已添加到队列: ${task.id} (优先级: ${task.priority})`);
    console.log(`📋 当前队列长度: ${this.queue.length}`);
  }

  /**
   * 从队列中移除任务
   * @param {number} taskId 任务ID
   */
  removeTask(taskId) {
    const index = this.queue.findIndex(task => task.id === taskId);
    if (index !== -1) {
      this.queue.splice(index, 1);
      console.log(`📋 任务已从队列移除: ${taskId}`);
    }

    // 如果任务正在运行，也要停止
    if (this.running.has(taskId)) {
      this.running.delete(taskId);
      console.log(`📋 运行中任务已停止: ${taskId}`);
    }
  }

  /**
   * 获取下一个待执行的任务
   */
  getNextTask() {
    // 检查是否达到最大并发数
    if (this.running.size >= this.maxConcurrent) {
      return null;
    }

    // 获取队列中第一个任务（优先级最高）
    return this.queue.shift();
  }

  /**
   * 标记任务为运行中
   * @param {Object} task 任务对象
   */
  markTaskAsRunning(task) {
    this.running.set(task.id, {
      task,
      startTime: new Date()
    });
    console.log(`🏃 任务开始运行: ${task.id}`);
  }

  /**
   * 标记任务完成
   * @param {number} taskId 任务ID
   */
  markTaskAsCompleted(taskId) {
    if (this.running.has(taskId)) {
      const runningTask = this.running.get(taskId);
      const duration = Date.now() - runningTask.startTime.getTime();
      this.running.delete(taskId);
      console.log(`✅ 任务完成: ${taskId} (耗时: ${Math.round(duration / 1000)}秒)`);
    }
  }

  /**
   * 标记任务失败
   * @param {number} taskId 任务ID
   * @param {string} error 错误信息
   */
  markTaskAsFailed(taskId, error) {
    if (this.running.has(taskId)) {
      this.running.delete(taskId);
      console.log(`❌ 任务失败: ${taskId} - ${error}`);
    }
  }

  /**
   * 开始处理队列
   */
  startProcessing() {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    console.log('🚀 任务队列处理器启动');

    // 定期检查队列
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 停止处理队列
   */
  stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    this.isProcessing = false;
    console.log('⏹️ 任务队列处理器停止');
  }

  /**
   * 处理队列中的任务
   */
  async processQueue() {
    try {
      while (this.running.size < this.maxConcurrent && this.queue.length > 0) {
        const task = this.getNextTask();
        if (!task) break;

        // 标记为运行中
        this.markTaskAsRunning(task);

        // 异步执行任务（不等待完成）
        this.executeTask(task).catch(error => {
          console.error(`任务执行失败: ${task.id}`, error);
          this.markTaskAsFailed(task.id, error.message);
        });
      }
    } catch (error) {
      console.error('队列处理错误:', error);
    }
  }

  /**
   * 执行具体任务
   * @param {Object} task 任务对象
   */
  async executeTask(task) {
    try {
      console.log(`🎯 开始执行任务: ${task.id} - ${task.taskName}`);

      // 执行真实的爬虫逻辑
      await this.executeCrawlerTask(task);

      this.markTaskAsCompleted(task.id);
    } catch (error) {
      this.markTaskAsFailed(task.id, error.message);
      throw error;
    }
  }

  /**
   * 执行真实的爬虫任务
   * @param {Object} task 任务对象
   */
  async executeCrawlerTask(task) {
    const startTime = Date.now();

    try {
      // 记录任务开始日志
      await this.logTask(task.id, 'info', '任务开始执行', {
        taskId: task.id,
        platform: task.platform,
        keywords: task.keywords
      });

      // 更新任务状态为运行中
      await this.updateTaskStatus(task.id, 'running', {
        startedAt: new Date(),
        progress: 0
      });

      // 获取对应平台的爬虫实例
      const crawler = this.getCrawler(task.platform);
      if (!crawler) {
        throw new Error(`不支持的平台: ${task.platform}`);
      }

      // 检查Cookie可用性（仅对需要Cookie的平台）
      if (task.platform === 'juxingtu') {
        const cookieAvailable = await this.checkCookieAvailability(task.platform);
        if (!cookieAvailable) {
          throw new Error(`没有可用的${task.platform}Cookie，请先添加有效的Cookie后再执行爬取任务`);
        }
      }

      // 调试日志：打印任务配置（TaskQueue）
      console.log('🔍 [TaskQueue] 任务配置信息:');
      console.log('   任务ID:', task.id);
      console.log('   任务名称:', task.taskName);
      console.log('   平台:', task.platform);
      console.log('   原始config:', JSON.stringify(task.config, null, 2));
      console.log('   minFirstNotePlayCount from config:', task.config?.minFirstNotePlayCount);

      // 准备爬取配置 - 改进：优先使用用户配置，提供合理默认值
      const crawlConfig = {
        // 1. 首先设置系统必需的基础参数
        keywords: task.keywords,
        maxPages: task.maxPages || 5,
        crawlTaskId: task.id,
        startPage: task.currentPage || 1, // 从当前页面开始爬取（断点续传）

        // 2. 然后合并用户的完整配置（支持任意JSON结构）
        ...task.config,

        // 3. 最后设置有默认值的参数（只在用户未配置时使用默认值）
        pageSize: task.config?.pageSize || 20,
        delay: task.config?.delay || { min: 1000, max: 3000 },
        retries: task.config?.retries || 3,
        filters: task.config?.filters || {},
        saveVideos: task.config?.saveVideos !== false,
        minFirstNotePlayCount:
          task.config?.minFirstNotePlayCount !== undefined && task.config?.minFirstNotePlayCount !== null
            ? task.config?.minFirstNotePlayCount
            : 1 // 首个关联帖子最低播放量过滤阈值
      };

      // 调试日志：打印配置传递详情（TaskQueue）
      console.log('🔍 [TaskQueue] 配置传递详情:');
      console.log('   原始任务配置:', JSON.stringify(task.config, null, 2));
      console.log('   最终爬取配置:', JSON.stringify(crawlConfig, null, 2));
      console.log('   配置字段数量:', Object.keys(crawlConfig).length);
      console.log('   完整配置:', JSON.stringify(crawlConfig, null, 2));

      await this.logTask(task.id, 'info', '开始爬取数据', { config: crawlConfig });

      // 执行爬取
      const results = await crawler.crawl(crawlConfig, {
        onProgress: progress => this.handleProgress(task.id, progress),
        onResult: result => this.handleResult(task.id, result),
        onError: error => this.handleError(task.id, error)
      });

      // 任务完成
      await this.handleTaskCompletion(task.id, results);

      const duration = Date.now() - startTime;
      console.log(`✅ 任务执行完成: ${task.id} (耗时: ${Math.round(duration / 1000)}秒)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 任务执行失败: ${task.id} (耗时: ${Math.round(duration / 1000)}秒)`, error.message);

      // 记录错误日志
      await this.logTask(task.id, 'error', '任务执行失败', {
        error: error.message,
        duration
      });

      // 更新任务状态为失败
      await this.updateTaskStatus(task.id, 'failed', {
        completedAt: new Date(),
        errorMessage: error.message
      });

      throw error;
    }
  }

  /**
   * 获取爬虫实例
   * @param {string} platform 平台名称
   */
  getCrawler(platform) {
    return this.crawlers.get(platform);
  }

  /**
   * 检查Cookie可用性
   * @param {string} platform 平台名称
   */
  async checkCookieAvailability(platform) {
    try {
      const cookie = await this.cookieManager.getAvailableCookie(platform);
      return cookie !== null;
    } catch (error) {
      console.error(`检查${platform}Cookie可用性失败:`, error.message);
      return false;
    }
  }

  /**
   * 更新任务状态
   * @param {number} taskId 任务ID
   * @param {string} status 状态
   * @param {Object} updateData 更新数据
   */
  async updateTaskStatus(taskId, status, updateData = {}) {
    try {
      await CrawlTask.update(
        {
          status,
          ...updateData
        },
        {
          where: { id: taskId }
        }
      );
    } catch (error) {
      console.error(`更新任务状态失败: ${taskId}`, error.message);
    }
  }

  /**
   * 记录任务日志
   * @param {number} taskId 任务ID
   * @param {string} level 日志级别
   * @param {string} message 日志消息
   * @param {Object} details 详细信息
   */
  async logTask(taskId, level, message, details = {}) {
    try {
      await CrawlLog.create({
        taskId,
        level,
        message,
        details
      });
    } catch (error) {
      console.error(`记录任务日志失败: ${taskId}`, error.message);
    }
  }

  /**
   * 处理进度更新
   * @param {number} taskId 任务ID
   * @param {Object} progress 进度信息
   */
  async handleProgress(taskId, progress) {
    try {
      await this.updateTaskStatus(taskId, 'running', {
        progress: progress.percentage || 0,
        currentPage: progress.currentPage || 1
      });

      await this.logTask(taskId, 'info', '进度更新', progress);
      console.log(`📊 任务进度: ${taskId} - ${progress.percentage || 0}%`);
    } catch (error) {
      console.error(`处理进度更新失败: ${taskId}`, error.message);
    }
  }

  /**
   * 处理爬取结果
   * @param {number} taskId 任务ID
   * @param {Object} result 爬取结果
   */
  async handleResult(taskId, result) {
    try {
      // 从扩展信息中提取内容主题和达人标签
      const contentTheme = result.authorExtInfo?.content_theme_labels_180d || null;
      const influencerTags = result.authorExtInfo?.tags_relation || null;

      // 准备要保存的数据
      const influencerData = {
        taskId, // 任务ID
        nickname: result.nickname,
        avatarUrl: result.avatarUrl,
        followersCount: result.followersCount,
        city: result.city,
        uniqueId: result.uniqueId,
        contactInfo: result.contactInfo,
        videoStats: result.videoStats,
        videoDetails: result.videoDetails,
        rawData: result.rawData,
        authorExtInfo: result.authorExtInfo,
        contentTheme: contentTheme, // 提取的内容主题
        influencerTags: influencerTags, // 提取的达人标签
        playMid: result.playMid, // 播放量中位数
        status: 'pending'
      };

      // 检查是否已存在相同 platform 和 platformUserId 的记录
      const existingInfluencer = await PublicInfluencer.findOne({
        where: {
          platform: result.platform,
          platformUserId: result.platformUserId
        }
      });

      if (existingInfluencer) {
        // 更新现有记录，但保持原有的taskId
        await existingInfluencer.update(influencerData);

        console.log(`🔄 更新现有达人: ${result.nickname} (${result.platformUserId})`);
        console.log(`   任务ID: ${taskId} (更新)`);
        console.log(
          `   播放量中位数: ${result.playMid !== undefined && result.playMid !== null ? result.playMid : '无'} (更新)`
        );

        await this.logTask(taskId, 'info', '更新现有达人', {
          platformUserId: result.platformUserId,
          nickname: result.nickname,
          recordId: existingInfluencer.id,
          playMid: result.playMid,
          originalTaskId: existingInfluencer.taskId,
          currentTaskId: taskId
        });
      } else {
        // 创建新记录
        const newInfluencer = await PublicInfluencer.create({
          taskId,
          platform: result.platform,
          platformUserId: result.platformUserId,
          ...influencerData
        });

        console.log(`✨ 创建新达人: ${result.nickname} (${result.platformUserId})`);
        console.log(
          `   播放量中位数: ${result.playMid !== undefined && result.playMid !== null ? result.playMid : '无'} (新建)`
        );

        await this.logTask(taskId, 'info', '创建新达人', {
          platformUserId: result.platformUserId,
          nickname: result.nickname,
          recordId: newInfluencer.id,
          playMid: result.playMid
        });
      }
    } catch (error) {
      console.error(`保存爬取结果失败: ${taskId}`, error.message);
      await this.logTask(taskId, 'error', '保存结果失败', {
        error: error.message,
        platformUserId: result.platformUserId,
        nickname: result.nickname
      });
    }
  }

  /**
   * 处理爬取错误
   * @param {number} taskId 任务ID
   * @param {Error} error 错误对象
   */
  async handleError(taskId, error) {
    try {
      await this.logTask(taskId, 'error', '爬取过程出错', {
        error: error.message
      });
      console.error(`❌ 爬取错误: ${taskId} - ${error.message}`);
    } catch (logError) {
      console.error(`记录爬取错误失败: ${taskId}`, logError.message);
    }
  }

  /**
   * 处理任务完成
   * @param {number} taskId 任务ID
   * @param {Object} results 最终结果
   */
  async handleTaskCompletion(taskId, results) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (task) {
        await task.update({
          status: 'completed',
          completedAt: new Date(),
          progress: 100,
          totalCount: results.totalCount || 0,
          successCount: results.successCount || 0,
          failedCount: results.failedCount || 0,
          resultSummary: {
            totalResults: results.totalCount || 0,
            successCount: results.successCount || 0,
            failedCount: results.failedCount || 0,
            duration: task.startedAt ? Date.now() - task.startedAt.getTime() : 0
          }
        });
      }

      await this.logTask(taskId, 'info', '任务完成', results);
      console.log(`✅ 任务完成: ${taskId}`);
    } catch (error) {
      console.error(`任务完成处理失败: ${taskId}`, error.message);
    }
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      runningCount: this.running.size,
      maxConcurrent: this.maxConcurrent,
      isProcessing: this.isProcessing,
      runningTasks: Array.from(this.running.keys())
    };
  }

  /**
   * 设置最大并发数
   * @param {number} maxConcurrent 最大并发数
   */
  setMaxConcurrent(maxConcurrent) {
    this.maxConcurrent = Math.max(1, Math.min(10, maxConcurrent));
    console.log(`📋 最大并发数设置为: ${this.maxConcurrent}`);
  }

  /**
   * 清空队列
   */
  clearQueue() {
    this.queue = [];
    console.log('📋 队列已清空');
  }

  /**
   * 获取队列中的任务列表
   */
  getQueuedTasks() {
    return this.queue.map(task => ({
      id: task.id,
      taskName: task.taskName,
      platform: task.platform,
      priority: task.priority,
      keywords: task.keywords
    }));
  }

  /**
   * 获取正在运行的任务列表
   */
  getRunningTasks() {
    return Array.from(this.running.entries()).map(([taskId, info]) => ({
      id: taskId,
      taskName: info.task.taskName,
      platform: info.task.platform,
      startTime: info.startTime,
      duration: Date.now() - info.startTime.getTime()
    }));
  }
}

module.exports = TaskQueue;
