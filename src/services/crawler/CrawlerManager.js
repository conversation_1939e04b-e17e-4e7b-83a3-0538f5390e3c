/**
 * 爬虫管理器
 * 负责管理不同平台的爬虫实例和执行逻辑
 */

const XingtuCrawler = require('./crawlers/XingtuCrawler');
const XiaohongshuCrawler = require('./crawlers/XiaohongshuCrawler');
const { CrawlTask, PublicInfluencer, CrawlLog } = require('../../models');

class CrawlerManager {
  constructor() {
    this.crawlers = new Map();
    this.activeTasks = new Map(); // 活跃任务映射
    this.isInitialized = false;
  }

  /**
   * 初始化爬虫管理器
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // 注册爬虫实例
      this.registerCrawler('juxingtu', new XingtuCrawler());
      this.registerCrawler('xiaohongshu', new XiaohongshuCrawler());

      // 初始化所有爬虫
      for (const [platform, crawler] of this.crawlers) {
        await crawler.initialize();
        console.log(`✅ ${platform} 爬虫初始化成功`);
      }

      this.isInitialized = true;
      console.log('✅ 爬虫管理器初始化成功');
    } catch (error) {
      console.error('❌ 爬虫管理器初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 注册爬虫
   * @param {string} platform 平台名称
   * @param {Object} crawler 爬虫实例
   */
  registerCrawler(platform, crawler) {
    this.crawlers.set(platform, crawler);
    console.log(`📝 注册爬虫: ${platform}`);
  }

  /**
   * 获取爬虫实例
   * @param {string} platform 平台名称
   */
  getCrawler(platform) {
    const crawler = this.crawlers.get(platform);
    if (!crawler) {
      throw new Error(`不支持的平台: ${platform}`);
    }
    return crawler;
  }

  /**
   * 开始爬取任务
   * @param {Object} task 任务对象
   */
  async startCrawling(task) {
    try {
      // 检查任务是否已在运行
      if (this.activeTasks.has(task.id)) {
        throw new Error('任务已在运行中');
      }

      // 获取对应平台的爬虫
      const crawler = this.getCrawler(task.platform);

      // 检查Cookie可用性（对需要Cookie的平台）
      if (task.platform === 'juxingtu' || task.platform === 'xiaohongshu') {
        const cookieAvailable = await this.checkCookieAvailability(task.platform);
        if (!cookieAvailable) {
          const platformName = task.platform === 'juxingtu' ? '巨量星图' : '小红书';
          throw new Error(`没有可用的${platformName}Cookie，请先添加有效的Cookie后再执行爬取任务`);
        }
      }

      // 创建任务上下文
      const taskContext = {
        task,
        crawler,
        startTime: new Date(),
        status: 'running'
      };

      // 记录任务开始
      this.activeTasks.set(task.id, taskContext);
      await this.logTask(task.id, 'info', '任务开始执行', { taskId: task.id });

      // 更新任务状态
      await task.update({
        status: 'running',
        startedAt: new Date(),
        progress: 0
      });

      // 异步执行爬取任务
      this.executeCrawlingTask(taskContext).catch(async error => {
        console.error(`任务执行失败: ${task.id}`, error);
        await this.handleTaskError(task.id, error);
      });

      console.log(`🚀 开始爬取任务: ${task.id} - ${task.taskName}`);
    } catch (error) {
      console.error(`❌ 启动爬取任务失败: ${task.id}`, error.message);
      throw error;
    }
  }

  /**
   * 执行爬取任务
   * @param {Object} taskContext 任务上下文
   */
  async executeCrawlingTask(taskContext) {
    const { task, crawler } = taskContext;

    try {
      // 调试日志：打印任务配置
      console.log('🔍 [CrawlerManager] 任务配置信息:');
      console.log('   任务ID:', task.id);
      console.log('   任务名称:', task.taskName);
      console.log('   平台:', task.platform);
      console.log('   原始config:', JSON.stringify(task.config, null, 2));
      console.log('   minFirstNotePlayCount from config:', task.config?.minFirstNotePlayCount);

      // 准备爬取配置 - 改进：优先使用用户配置，提供合理默认值
      const crawlConfig = {
        // 1. 首先设置系统必需的基础参数
        keywords: task.config.keyword || task.config.keywords || task.keywords,
        keyword: task.config.keyword || task.config.keywords || task.keywords,
        maxPages: task.config.maxPages || task.maxPages || 5,
        crawlTaskId: task.id, // 自动添加任务ID，用于视频保存关联
        startPage: task.currentPage || 1, // 从当前页面开始爬取（断点续传）

        // 2. 然后合并用户的完整配置（支持任意JSON结构）
        ...task.config,
        // 3. 最后设置有默认值的参数（只在用户未配置时使用默认值）
        sendParams: task.config?.sendParams || {},
        pageSize: task.config?.pageSize || 20,
        delay: task.config?.delay || { min: 1000, max: 3000 },
        retries: task.config?.retries || 3,
        filters: task.config?.filters || {},
        saveVideos: task.config?.saveVideos !== false // 默认保存视频，除非明确禁用
      };

      // 调试日志：打印配置传递详情
      console.log('🔍 [CrawlerManager] 配置传递详情:');
      console.log('   原始任务配置:', JSON.stringify(task.config, null, 2));
      console.log('   最终爬取配置:', JSON.stringify(crawlConfig, null, 2));
      console.log('   配置字段数量:', Object.keys(crawlConfig).length);
      console.log(
        '   用户自定义字段:',
        Object.keys(task.config || {}).filter(
          key => !['pageSize', 'delay', 'retries', 'filters', 'saveVideos', 'minFirstNotePlayCount'].includes(key)
        )
      );

      await this.logTask(task.id, 'info', '开始爬取数据', { config: crawlConfig });

      // 执行爬取
      const results = await crawler.crawl(crawlConfig, {
        onProgress: progress => this.handleProgress(task.id, progress),
        onResult: result => this.handleResult(task.id, result),
        onError: error => this.handleError(task.id, error)
      });

      // 任务完成
      await this.handleTaskCompletion(task.id, results);
    } catch (error) {
      // 检查是否是Cookie相关错误
      if (error.message.includes('没有可用的') && error.message.includes('Cookie')) {
        await this.logTask(task.id, 'error', `Cookie不可用: ${error.message}`, { error: error.message });
        await this.handleTaskError(task.id, error, 'cookie_unavailable');
      } else {
        await this.handleTaskError(task.id, error);
      }
    }
  }

  /**
   * 停止爬取任务
   * @param {number} taskId 任务ID
   */
  async stopCrawling(taskId) {
    try {
      // 调试日志：打印当前活跃任务状态
      console.log(`🔍 [CrawlerManager] 停止爬取任务 ${taskId}:`);
      console.log(`   当前活跃任务数量: ${this.activeTasks.size}`);
      console.log(`   活跃任务列表: [${Array.from(this.activeTasks.keys()).join(', ')}]`);

      const taskContext = this.activeTasks.get(taskId);
      if (!taskContext) {
        console.log(`⚠️ [CrawlerManager] 任务 ${taskId} 不在活跃任务列表中`);
        console.log(`   可能原因: 任务已完成、已失败或从未启动`);
        throw new Error('任务未在运行中');
      }

      console.log(`✅ [CrawlerManager] 找到活跃任务 ${taskId}，准备停止`);
      console.log(`   爬虫类型: ${taskContext.crawler.constructor.name}`);
      console.log(`   任务开始时间: ${taskContext.startTime}`);

      // 停止爬虫
      if (taskContext.crawler && taskContext.crawler.stop) {
        console.log(`🛑 [CrawlerManager] 调用爬虫停止方法`);
        await taskContext.crawler.stop(taskId);
      } else {
        console.log(`⚠️ [CrawlerManager] 爬虫没有停止方法，直接移除任务`);
      }

      // 移除活跃任务
      this.activeTasks.delete(taskId);
      console.log(`🗑️ [CrawlerManager] 已从活跃任务列表中移除任务 ${taskId}`);

      await this.logTask(taskId, 'info', '任务已停止');
      console.log(`⏹️ 停止爬取任务: ${taskId}`);
    } catch (error) {
      console.error(`❌ 停止爬取任务失败: ${taskId}`, error.message);
      throw error;
    }
  }

  /**
   * 处理任务进度更新
   * @param {number} taskId 任务ID
   * @param {Object} progress 进度信息
   */
  async handleProgress(taskId, progress) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (task) {
        await task.update({
          progress: progress.percentage,
          currentPage: progress.currentPage,
          successCount: progress.successCount,
          failedCount: progress.failedCount,
          totalCount: progress.successCount + progress.failedCount || 0
        });
      }

      await this.logTask(taskId, 'info', '进度更新', progress);
    } catch (error) {
      console.error(`进度更新失败: ${taskId}`, error);
    }
  }

  /**
   * 处理爬取结果
   * @param {number} taskId 任务ID
   * @param {Object} result 爬取结果
   */
  async handleResult(taskId, result) {
    try {
      // 从扩展信息中提取内容主题和达人标签
      const contentTheme = result.authorExtInfo?.content_theme_labels_180d || null;
      const influencerTags = result.authorExtInfo?.tags_relation || null;

      // 检查是否已存在相同 platformUserId 的记录
      const existingInfluencer = await PublicInfluencer.findOne({
        where: {
          platform: result.platform,
          platformUserId: result.platformUserId
        }
      });

      const influencerData = {
        nickname: result.nickname,
        avatarUrl: result.avatarUrl,
        followersCount: result.followersCount,
        city: result.city,
        uniqueId: result.uniqueId,
        contactInfo: result.contactInfo,
        videoStats: result.videoStats,
        videoDetails: result.videoDetails,
        rawData: result.rawData,
        authorExtInfo: result.authorExtInfo, // 保存扩展信息
        contentTheme: contentTheme, // 提取的内容主题
        influencerTags: influencerTags, // 提取的达人标签
        playMid: result.playMid, // 提取的播放量中位数
        status: 'pending'
      };

      if (existingInfluencer) {
        // 更新现有记录，包含当前任务ID
        await existingInfluencer.update({
          ...influencerData,
          taskId: taskId // 将当前爬虫任务ID赋值给更新的达人记录
        });

        console.log(`🔄 更新现有达人: ${result.nickname} (${result.platformUserId})`);
        console.log(`   任务ID: ${taskId} (已更新)`);
        console.log(
          `   播放量中位数: ${result.playMid !== undefined && result.playMid !== null ? result.playMid : '无'} (更新)`
        );

        await this.logTask(taskId, 'info', '更新现有达人', {
          platformUserId: result.platformUserId,
          nickname: result.nickname,
          recordId: existingInfluencer.id,
          playMid: result.playMid
        });
      } else {
        // 创建新记录
        const newInfluencer = await PublicInfluencer.create({
          taskId,
          platform: result.platform,
          platformUserId: result.platformUserId,
          ...influencerData
        });

        console.log(`✨ 创建新达人: ${result.nickname} (${result.platformUserId})`);
        console.log(
          `   播放量中位数: ${result.playMid !== undefined && result.playMid !== null ? result.playMid : '无'} (新建)`
        );

        await this.logTask(taskId, 'info', '创建新达人', {
          platformUserId: result.platformUserId,
          nickname: result.nickname,
          recordId: newInfluencer.id,
          playMid: result.playMid
        });
      }
    } catch (error) {
      console.error(`保存爬取结果失败: ${taskId}`, error);
      await this.logTask(taskId, 'error', '保存结果失败', {
        error: error.message,
        platformUserId: result.platformUserId,
        nickname: result.nickname
      });
    }
  }

  /**
   * 处理爬取错误
   * @param {number} taskId 任务ID
   * @param {Error} error 错误对象
   */
  async handleError(taskId, error) {
    await this.logTask(taskId, 'error', '爬取过程中发生错误', {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 处理任务完成
   * @param {number} taskId 任务ID
   * @param {Object} results 最终结果
   */
  async handleTaskCompletion(taskId, results) {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (task) {
        await task.update({
          status: 'completed',
          completedAt: new Date(),
          progress: 100,
          resultSummary: {
            totalResults: results.totalCount,
            successCount: results.successCount,
            failedCount: results.failedCount,
            duration: Date.now() - task.startedAt.getTime()
          }
        });
      }

      // 更新所有该任务的爬取结果状态为 'processed'
      await this.updateCrawlResultsStatus(taskId);

      // 移除活跃任务
      this.activeTasks.delete(taskId);

      await this.logTask(taskId, 'info', '任务完成', results);
      console.log(`✅ 任务完成: ${taskId}`);
    } catch (error) {
      console.error(`任务完成处理失败: ${taskId}`, error);
    }
  }

  /**
   * 更新爬取结果状态
   * @param {number} taskId 任务ID
   */
  async updateCrawlResultsStatus(taskId) {
    try {
      // 将该任务下所有状态为 'pending' 的结果更新为 'processed'
      const updateResult = await PublicInfluencer.update(
        { status: 'processed' },
        {
          where: {
            taskId: taskId,
            status: 'pending'
          }
        }
      );

      const updatedCount = updateResult[0];
      if (updatedCount > 0) {
        await this.logTask(taskId, 'info', '更新爬取结果状态', {
          updatedCount: updatedCount,
          newStatus: 'processed'
        });
        console.log(`✅ 更新了 ${updatedCount} 个爬取结果的状态为 'processed'`);
      } else {
        console.log(`ℹ️ 任务 ${taskId} 没有需要更新状态的爬取结果`);
      }
    } catch (error) {
      console.error(`更新爬取结果状态失败: ${taskId}`, error);
      await this.logTask(taskId, 'error', '更新结果状态失败', { error: error.message });
    }
  }

  /**
   * 处理任务错误
   * @param {number} taskId 任务ID
   * @param {Error} error 错误对象
   * @param {string} errorType 错误类型
   */
  async handleTaskError(taskId, error, errorType = 'general') {
    try {
      const task = await CrawlTask.findByPk(taskId);
      if (task) {
        // 根据错误类型设置不同的状态
        let status = 'failed';
        let errorMessage = error.message;

        if (errorType === 'cookie_unavailable') {
          status = 'pending'; // Cookie不可用时，任务状态改为待处理，等待Cookie可用后重试
          errorMessage = `Cookie不可用: ${error.message}`;
        }

        await task.update({
          status: status,
          completedAt: errorType === 'cookie_unavailable' ? null : new Date(),
          errorMessage: errorMessage
        });
      }

      // 如果是一般错误（非Cookie问题），更新爬取结果状态为失败
      if (errorType !== 'cookie_unavailable') {
        await this.updateCrawlResultsStatusOnError(taskId, error.message);
      }

      // 移除活跃任务
      this.activeTasks.delete(taskId);

      const logMessage = errorType === 'cookie_unavailable' ? 'Cookie不可用，任务暂停等待Cookie' : '任务执行失败';

      await this.logTask(taskId, 'error', logMessage, {
        error: error.message,
        errorType: errorType,
        stack: error.stack
      });

      const statusMessage =
        errorType === 'cookie_unavailable'
          ? `⏸️ 任务暂停(Cookie不可用): ${taskId} - ${error.message}`
          : `❌ 任务失败: ${taskId} - ${error.message}`;

      console.log(statusMessage);
    } catch (logError) {
      console.error(`任务错误处理失败: ${taskId}`, logError);
    }
  }

  /**
   * 更新爬取结果状态为失败
   * @param {number} taskId 任务ID
   * @param {string} errorMessage 错误信息
   */
  async updateCrawlResultsStatusOnError(taskId, errorMessage) {
    try {
      // 将该任务下所有状态为 'pending' 的结果更新为 'failed'
      const updateResult = await PublicInfluencer.update(
        {
          status: 'failed',
          errorMessage: errorMessage
        },
        {
          where: {
            taskId: taskId,
            status: 'pending'
          }
        }
      );

      const updatedCount = updateResult[0];
      if (updatedCount > 0) {
        await this.logTask(taskId, 'info', '更新失败结果状态', {
          updatedCount: updatedCount,
          newStatus: 'failed'
        });
        console.log(`✅ 更新了 ${updatedCount} 个爬取结果的状态为 'failed'`);
      }
    } catch (error) {
      console.error(`更新失败结果状态失败: ${taskId}`, error);
      await this.logTask(taskId, 'error', '更新失败结果状态失败', { error: error.message });
    }
  }

  /**
   * 记录任务日志
   * @param {number} taskId 任务ID
   * @param {string} level 日志级别
   * @param {string} message 日志消息
   * @param {Object} details 详细信息
   */
  async logTask(taskId, level, message, details = {}) {
    try {
      await CrawlLog.create({
        taskId,
        level,
        message,
        details,
        step: details.step || 'general'
      });
    } catch (error) {
      console.error('记录任务日志失败:', error);
    }
  }

  /**
   * 检查Cookie可用性
   * @param {string} platform 平台名称
   * @returns {boolean} Cookie是否可用
   */
  async checkCookieAvailability(platform) {
    try {
      const crawler = this.getCrawler(platform);
      if (crawler && crawler.cookieManager) {
        const cookie = await crawler.cookieManager.getAvailableCookie(platform);
        return !!cookie;
      }
      return false;
    } catch (error) {
      console.error(`检查${platform}Cookie可用性失败:`, error.message);
      return false;
    }
  }

  /**
   * 获取管理器状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      registeredCrawlers: Array.from(this.crawlers.keys()),
      activeTasks: Array.from(this.activeTasks.keys()),
      activeTaskCount: this.activeTasks.size
    };
  }

  /**
   * 获取支持的平台列表
   */
  getSupportedPlatforms() {
    return Array.from(this.crawlers.keys());
  }
}

module.exports = CrawlerManager;
