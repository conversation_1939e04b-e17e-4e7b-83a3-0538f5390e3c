// 从 xingtu/vendor.js 逆向拆解的 sign$1 加密方法实现
// MD5 实现 (从原代码中提取)
const md5 = (function () {
  var crypt = {
    rotl: function (l, d) {
      return (l << d) | (l >>> (32 - d));
    },
    rotr: function (l, d) {
      return (l << (32 - d)) | (l >>> d);
    },
    endian: function (l) {
      if (l.constructor == Number) return (crypt.rotl(l, 8) & 16711935) | (crypt.rotl(l, 24) & 4278255360);
      for (var d = 0; d < l.length; d++) l[d] = crypt.endian(l[d]);
      return l;
    },
    bytesToWords: function (l) {
      for (var d = [], p = 0, v = 0; p < l.length; p++, v += 8) d[v >>> 5] |= l[p] << (24 - (v % 32));
      return d;
    },
    wordsToBytes: function (l) {
      for (var d = [], p = 0; p < l.length * 32; p += 8) d.push((l[p >>> 5] >>> (24 - (p % 32))) & 255);
      return d;
    },
    bytesToHex: function (l) {
      for (var d = [], p = 0; p < l.length; p++) d.push((l[p] >>> 4).toString(16)), d.push((l[p] & 15).toString(16));
      return d.join('');
    }
  };

  var charenc = {
    utf8: {
      stringToBytes: function (s) {
        return charenc.bin.stringToBytes(unescape(encodeURIComponent(s)));
      }
    },
    bin: {
      stringToBytes: function (s) {
        for (var c = [], l = 0; l < s.length; l++) c.push(s.charCodeAt(l) & 255);
        return c;
      },
      bytesToString: function (s) {
        for (var c = [], l = 0; l < s.length; l++) c.push(String.fromCharCode(s[l]));
        return c.join('');
      }
    }
  };

  var md5Hash = function (v, g) {
    var c = charenc.utf8;
    var d = charenc.bin;

    v.constructor == String ? (v = c.stringToBytes(v)) : Array.isArray(v) ? (v = v) : (v = v.toString());

    var y = crypt.bytesToWords(v),
      m = v.length * 8,
      b = 1732584193,
      T = -271733879,
      w = -1732584194,
      O = 271733878;

    for (var I = 0; I < y.length; I++)
      y[I] = (((y[I] << 8) | (y[I] >>> 24)) & 16711935) | (((y[I] << 24) | (y[I] >>> 8)) & 4278255360);

    y[m >>> 5] |= 128 << m % 32;
    y[(((m + 64) >>> 9) << 4) + 14] = m;

    var _ff = function (v, g, y, m, b, T, w) {
      var O = v + ((g & y) | (~g & m)) + (b >>> 0) + w;
      return ((O << T) | (O >>> (32 - T))) + g;
    };
    var _gg = function (v, g, y, m, b, T, w) {
      var O = v + ((g & m) | (y & ~m)) + (b >>> 0) + w;
      return ((O << T) | (O >>> (32 - T))) + g;
    };
    var _hh = function (v, g, y, m, b, T, w) {
      var O = v + (g ^ y ^ m) + (b >>> 0) + w;
      return ((O << T) | (O >>> (32 - T))) + g;
    };
    var _ii = function (v, g, y, m, b, T, w) {
      var O = v + (y ^ (g | ~m)) + (b >>> 0) + w;
      return ((O << T) | (O >>> (32 - T))) + g;
    };

    for (var I = 0; I < y.length; I += 16) {
      var F = b,
        V = T,
        q = w,
        z = O;
      b = _ff(b, T, w, O, y[I + 0], 7, -680876936);
      O = _ff(O, b, T, w, y[I + 1], 12, -389564586);
      w = _ff(w, O, b, T, y[I + 2], 17, 606105819);
      T = _ff(T, w, O, b, y[I + 3], 22, -1044525330);
      b = _ff(b, T, w, O, y[I + 4], 7, -176418897);
      O = _ff(O, b, T, w, y[I + 5], 12, 1200080426);
      w = _ff(w, O, b, T, y[I + 6], 17, -1473231341);
      T = _ff(T, w, O, b, y[I + 7], 22, -45705983);
      b = _ff(b, T, w, O, y[I + 8], 7, 1770035416);
      O = _ff(O, b, T, w, y[I + 9], 12, -1958414417);
      w = _ff(w, O, b, T, y[I + 10], 17, -42063);
      T = _ff(T, w, O, b, y[I + 11], 22, -1990404162);
      b = _ff(b, T, w, O, y[I + 12], 7, 1804603682);
      O = _ff(O, b, T, w, y[I + 13], 12, -40341101);
      w = _ff(w, O, b, T, y[I + 14], 17, -1502002290);
      T = _ff(T, w, O, b, y[I + 15], 22, 1236535329);
      b = _gg(b, T, w, O, y[I + 1], 5, -165796510);
      O = _gg(O, b, T, w, y[I + 6], 9, -1069501632);
      w = _gg(w, O, b, T, y[I + 11], 14, 643717713);
      T = _gg(T, w, O, b, y[I + 0], 20, -373897302);
      b = _gg(b, T, w, O, y[I + 5], 5, -701558691);
      O = _gg(O, b, T, w, y[I + 10], 9, 38016083);
      w = _gg(w, O, b, T, y[I + 15], 14, -660478335);
      T = _gg(T, w, O, b, y[I + 4], 20, -405537848);
      b = _gg(b, T, w, O, y[I + 9], 5, 568446438);
      O = _gg(O, b, T, w, y[I + 14], 9, -1019803690);
      w = _gg(w, O, b, T, y[I + 3], 14, -187363961);
      T = _gg(T, w, O, b, y[I + 8], 20, 1163531501);
      b = _gg(b, T, w, O, y[I + 13], 5, -1444681467);
      O = _gg(O, b, T, w, y[I + 2], 9, -51403784);
      w = _gg(w, O, b, T, y[I + 7], 14, 1735328473);
      T = _gg(T, w, O, b, y[I + 12], 20, -1926607734);
      b = _hh(b, T, w, O, y[I + 5], 4, -378558);
      O = _hh(O, b, T, w, y[I + 8], 11, -2022574463);
      w = _hh(w, O, b, T, y[I + 11], 16, 1839030562);
      T = _hh(T, w, O, b, y[I + 14], 23, -35309556);
      b = _hh(b, T, w, O, y[I + 1], 4, -1530992060);
      O = _hh(O, b, T, w, y[I + 4], 11, 1272893353);
      w = _hh(w, O, b, T, y[I + 7], 16, -155497632);
      T = _hh(T, w, O, b, y[I + 10], 23, -1094730640);
      b = _hh(b, T, w, O, y[I + 13], 4, 681279174);
      O = _hh(O, b, T, w, y[I + 0], 11, -358537222);
      w = _hh(w, O, b, T, y[I + 3], 16, -722521979);
      T = _hh(T, w, O, b, y[I + 6], 23, 76029189);
      b = _hh(b, T, w, O, y[I + 9], 4, -640364487);
      O = _hh(O, b, T, w, y[I + 12], 11, -421815835);
      w = _hh(w, O, b, T, y[I + 15], 16, 530742520);
      T = _hh(T, w, O, b, y[I + 2], 23, -995338651);
      b = _ii(b, T, w, O, y[I + 0], 6, -198630844);
      O = _ii(O, b, T, w, y[I + 7], 10, 1126891415);
      w = _ii(w, O, b, T, y[I + 14], 15, -1416354905);
      T = _ii(T, w, O, b, y[I + 5], 21, -57434055);
      b = _ii(b, T, w, O, y[I + 12], 6, 1700485571);
      O = _ii(O, b, T, w, y[I + 3], 10, -1894986606);
      w = _ii(w, O, b, T, y[I + 10], 15, -1051523);
      T = _ii(T, w, O, b, y[I + 1], 21, -2054922799);
      b = _ii(b, T, w, O, y[I + 8], 6, 1873313359);
      O = _ii(O, b, T, w, y[I + 15], 10, -30611744);
      w = _ii(w, O, b, T, y[I + 6], 15, -1560198380);
      T = _ii(T, w, O, b, y[I + 13], 21, 1309151649);
      b = _ii(b, T, w, O, y[I + 4], 6, -145523070);
      O = _ii(O, b, T, w, y[I + 11], 10, -1120210379);
      w = _ii(w, O, b, T, y[I + 2], 15, 718787259);
      T = _ii(T, w, O, b, y[I + 9], 21, -343485551);
      b = (b + F) >>> 0;
      T = (T + V) >>> 0;
      w = (w + q) >>> 0;
      O = (O + z) >>> 0;
    }
    return crypt.endian([b, T, w, O]);
  };

  return function (v, g) {
    if (v == null) throw new Error('Illegal argument ' + v);
    var y = crypt.wordsToBytes(md5Hash(v, g));
    return crypt.bytesToHex(y);
  };
})();

// 辅助函数
function isInvalid(s) {
  return s == null || Number.isNaN(s);
}

function isScalar(s) {
  return ['string', 'number'].includes(typeof s);
}

function isIncompatible(s) {
  return Array.isArray(s) || typeof s == 'boolean' || Number.isNaN(s);
}

function normalizeData(s) {
  if (!s || typeof s != 'object') return s;
  const c = {};
  for (const l of Object.keys(s)) {
    const d = s[l];
    c[l] = isIncompatible(d) ? JSON.stringify(d) : d;
  }
  return c;
}

// 核心签名函数 (从原代码逆向)
function sign$1(s, c, l) {
  const { include: d, enforceWithKeys: p = [] } = c != null ? c : {};
  let v = Object.keys(s);
  if (l && d) {
    const m = d.concat(['service_name', 'service_method', 'sign_strict']);
    v = v.filter(b => m.includes(b));
  }
  const g = v
    .sort()
    .map(m => {
      const b = s[m];
      return isInvalid(b) ? '' : m + (!p.includes(m) && isScalar(b) ? b : m);
    })
    .join('');
  return md5(g + 'e9fefef711becf4c3d7bfef829578b0c');
}

// 生成完整载荷的函数
function generatePayload(s, c = {}) {
  const { strict: l, serializing: d, rule: p } = c;
  const v = {
    sign_strict: l ? 1 : void 0
  };
  return {
    ...(d ? normalizeData(s) : s),
    ...v,
    sign: sign$1(
      {
        ...s,
        ...v
      },
      p,
      l
    )
  };
}

// 生成签名
// const result = generatePayload(s, {
//   strict: l,
//   serializing: true,
//   rule: p,
// });

// console.log("生成的载荷:", result);
// console.log("签名:", result.sign);

const easySign = (author_id, limit = 50) => {
  // 使用示例
  const s = {
    display_scene: 32,
    page: 1,
    limit: limit,
    platform_channel: 1,
    request_source: 18,
    recommend_strategy: 11,
    extra_info: {
      author_ids: `["${author_id}"]`,
      similar_type: '"comprehension"'
    },
    service_name: 'go_search.AdStarGoSearchService',
    service_method: 'RecommendForStarAuthors'
  };

  const v = {
    sign_strict: 1
  };

  const p = {
    include: [
      'request_source',
      'platform_channel',
      'display_scene',
      'recommend_strategy',
      'page',
      'limit',
      'extra_info',
      'post_recommend_info',
      'first_industry_id',
      'yuntu_brand_id'
    ]
  };

  const l = true;

  let result = generatePayload(s, {
    strict: l,
    serializing: true,
    rule: p
  });

  return result.sign;
};

console.log(easySign('226629722305331200008'));

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    sign$1,
    generatePayload,
    normalizeData,
    md5,
    easySign
  };
}
