/**
 * CrawlerTaskModal 组件测试
 * 
 * 测试组件的基本功能和修复的初始化问题
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import CrawlerTaskModal from '../CrawlerTaskModal.vue';

describe('CrawlerTaskModal', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(CrawlerTaskModal, {
      props: {
        visible: true,
        editingTask: null
      }
    });
  });

  it('应该正确渲染组件', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.arco-modal').exists()).toBe(true);
  });

  it('应该在创建模式下显示正确的标题', () => {
    expect(wrapper.find('.arco-modal-title').text()).toBe('创建任务');
  });

  it('应该在编辑模式下显示正确的标题', async () => {
    await wrapper.setProps({
      editingTask: {
        id: 1,
        taskName: '测试任务',
        platform: 'xiaohongshu',
        keywords: '测试关键词',
        maxPages: 5,
        priority: 0,
        config: {}
      }
    });

    expect(wrapper.find('.arco-modal-title').text()).toBe('编辑任务');
  });

  it('应该正确初始化表单数据', () => {
    // 验证组件能够正常初始化，不会抛出 resetForm 错误
    expect(wrapper.vm).toBeDefined();
  });

  it('应该显示平台选择卡片', () => {
    const platformCards = wrapper.findAll('.platform-card');
    expect(platformCards.length).toBeGreaterThan(0);
  });

  it('应该包含必要的表单字段', () => {
    expect(wrapper.find('input[placeholder="请输入任务名称"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="请输入搜索关键词，多个关键词用空格分隔"]').exists()).toBe(true);
  });

  it('应该在编辑模式下正确加载任务数据', async () => {
    const taskData = {
      id: 1,
      taskName: '测试任务',
      platform: 'xiaohongshu',
      keywords: '测试关键词',
      maxPages: 10,
      priority: 5,
      config: {
        minFirstNotePlayCount: 1000,
        searchType: 1
      }
    };

    await wrapper.setProps({ editingTask: taskData });

    // 验证表单数据是否正确加载
    const taskNameInput = wrapper.find('input[placeholder="请输入任务名称"]');
    expect(taskNameInput.element.value).toBe('测试任务');

    const keywordsInput = wrapper.find('input[placeholder="请输入搜索关键词，多个关键词用空格分隔"]');
    expect(keywordsInput.element.value).toBe('测试关键词');
  });

  it('应该正确处理平台选择', async () => {
    const xiaohongshuCard = wrapper.find('.platform-card[data-platform="xiaohongshu"]');
    if (xiaohongshuCard.exists()) {
      await xiaohongshuCard.trigger('click');
      expect(wrapper.find('.platform-card-selected').exists()).toBe(true);
    }
  });

  it('应该在取消时触发正确的事件', async () => {
    const cancelButton = wrapper.find('.arco-modal-footer .arco-btn:not(.arco-btn-primary)');
    if (cancelButton.exists()) {
      await cancelButton.trigger('click');
      expect(wrapper.emitted('cancel')).toBeTruthy();
    }
  });
});

// 集成测试：验证组件初始化不会抛出错误
describe('CrawlerTaskModal 初始化测试', () => {
  it('应该能够正常初始化而不抛出 resetForm 错误', () => {
    expect(() => {
      mount(CrawlerTaskModal, {
        props: {
          visible: true,
          editingTask: null
        }
      });
    }).not.toThrow();
  });

  it('应该能够在编辑模式下正常初始化', () => {
    expect(() => {
      mount(CrawlerTaskModal, {
        props: {
          visible: true,
          editingTask: {
            id: 1,
            taskName: '测试任务',
            platform: 'xiaohongshu',
            keywords: '测试',
            maxPages: 5,
            priority: 0,
            config: {}
          }
        }
      });
    }).not.toThrow();
  });

  it('应该能够在 props 变化时正常响应', async () => {
    const wrapper = mount(CrawlerTaskModal, {
      props: {
        visible: true,
        editingTask: null
      }
    });

    // 从创建模式切换到编辑模式
    await wrapper.setProps({
      editingTask: {
        id: 1,
        taskName: '测试任务',
        platform: 'xiaohongshu',
        keywords: '测试',
        maxPages: 5,
        priority: 0,
        config: {}
      }
    });

    // 从编辑模式切换回创建模式
    await wrapper.setProps({
      editingTask: null
    });

    // 验证没有抛出错误
    expect(wrapper.exists()).toBe(true);
  });
});
