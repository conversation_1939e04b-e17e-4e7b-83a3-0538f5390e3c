<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="isEditMode ? '编辑任务' : '创建任务'"
    width="900px"
    :on-before-ok="handleSubmit"
    :confirm-loading="submitLoading"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="config" :rules="rules" :auto-label-width="true">
      <a-form-item label="任务名称" field="taskName">
        <a-input style="width: 100%" v-model="taskName" placeholder="请输入任务名称" />
      </a-form-item>

      <!-- 平台选择 - 平铺展示 -->
      <a-form-item label="选择平台" field="platform">
        <div class="platform-selection">
          <div
            v-for="platformItem in availablePlatforms"
            :key="platformItem.value"
            class="platform-card"
            :class="{ 'platform-card-selected': platform === platformItem.value }"
            @click="selectPlatform(platformItem.value)"
          >
            <div class="platform-icon">
              <img :src="platformItem.icon" :alt="platformItem.name" class="platform-logo" />
            </div>
            <div class="platform-info">
              <div class="platform-name">{{ platformItem.name }}</div>
              <div class="platform-desc">{{ platformItem.description }}</div>
            </div>
            <div class="platform-status" v-if="platform === platformItem.value">
              <icon-check class="check-icon" />
            </div>
          </div>
        </div>
      </a-form-item>

      <!-- 平台专用配置 -->
      <div v-if="platform" class="platform-specific-config">
        <a-divider orientation="left">
          <span class="config-section-title">
            <img
              :src="getPlatformInfo(platform)?.icon"
              :alt="getPlatformInfo(platform)?.name"
              class="platform-logo-small"
            />
            {{ getPlatformInfo(platform)?.name }} 专用配置
          </span>
        </a-divider>

        <!-- 小红书专用配置 -->
        <div v-if="platform === 'xiaohongshu'" class="xiaohongshu-config">
          <!-- 单选 决定爬虫的方式 -->
          <a-form-item
            label="爬虫方式"
            field="config.clawType"
            :rules="[{ required: true, message: '请选择爬虫方式' }]"
          >
            <a-radio-group type="button" v-model="clawType">
              <a-radio :value="1">笔记广场</a-radio>
              <a-radio :value="2">内容广场</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 笔记广场 -->
          <template v-if="clawType === 1">
            <a-form-item
              label="搜索关键词"
              field="config.keywords"
              :rules="[{ required: true, message: '请输入搜索关键词' }]"
            >
              <a-input v-model="keywords" placeholder="请输入搜索关键词" />
              <a-radio-group type="button" style="min-width: 140px" v-model="searchType">
                <a-radio :value="1">搜笔记</a-radio>
                <a-radio :value="0">搜昵称</a-radio>
              </a-radio-group>
            </a-form-item>
          </template>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大页数" field="maxPages">
                <a-input-number
                  v-model="maxPages"
                  placeholder="请输入最大爬取页数"
                  :min="1"
                  :max="50"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务优先级" field="priority">
                <a-input-number
                  v-model="priority"
                  placeholder="数字越大优先级越高"
                  :min="0"
                  :max="10"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 巨量星图专用配置 -->
        <div v-else-if="platform === 'juxingtu'" class="juxingtu-config">
          <!-- 单选 决定爬虫的方式 -->
          <a-form-item
            label="爬虫方式"
            field="config.clawType"
            :rules="[{ required: true, message: '请选择爬虫方式' }]"
          >
            <a-radio-group type="button" v-model="clawType">
              <a-radio :value="1">短视频广场</a-radio>
              <a-radio :value="2">内容灵感</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- 短视频广场 -->
          <template v-if="clawType === 1">
            <a-form-item
              label="搜索关键词"
              field="config.keywords"
              :rules="[{ required: true, message: '请输入搜索关键词' }]"
            >
              <a-input v-model="keywords" placeholder="请输入搜索关键词" />
              <a-radio-group type="button" style="min-width: 180px" v-model="searchType">
                <a-radio :value="1">内容找人</a-radio>
                <a-radio :value="0">昵称找人</a-radio>
              </a-radio-group>
            </a-form-item>
          </template>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大页数" field="maxPages">
                <a-input-number
                  v-model="maxPages"
                  placeholder="请输入最大爬取页数"
                  :min="1"
                  :max="50"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="任务优先级" field="priority">
                <a-input-number
                  v-model="priority"
                  placeholder="数字越大优先级越高"
                  :min="0"
                  :max="10"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <a-form-item label="首个作品最低阅读量" field="config.minFirstNotePlayCount">
          <a-input-number
            v-model="minFirstNotePlayCount"
            placeholder="请输入最低播放量要求"
            :min="0"
            :max="10000000"
            style="width: 100%"
          />
          <template #help>
            <div class="config-help-text">
              <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
                用于过滤达人的首个关联帖子播放量，设置为0表示不过滤
              </a-typography-text>
            </div>
          </template>
        </a-form-item>
        <!-- 相似达人抓取 -->
        <a-form-item label="开启相似达人抓取" field="config.similarKolIds">
          <a-switch v-model="openSimilarKol">开启</a-switch>
          <!-- 开启后输入个数量 -->
          <a-input-number
            v-if="openSimilarKol"
            v-model="similarKolCount"
            placeholder="请输入相似达人数量"
            :min="1"
            :max="1000"
            hide-button
            style="width: 120px; margin-left: 10px"
          >
            <template #suffix>个</template>
          </a-input-number>
          <template #help>
            <div class="config-help-text">
              <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
                开启后，会自动抓取与目标达人的相似达人
              </a-typography-text>
            </div>
          </template>
        </a-form-item>
      </div>

      <!-- 平台查询参数 -->
      <a-divider orientation="left">
        <span class="config-section-title">平台查询参数</span>
      </a-divider>

      <a-form-item label="平台查询参数" field="sendParams">
        <a-textarea
          v-model="sendParamsJson"
          @input="updateSendParamsJson"
          placeholder='请粘贴从平台复制的原始查询参数，如：{"page": 1, "size": 20, "sort": "hot"}'
          :rows="3"
          :auto-size="{ minRows: 3, maxRows: 8 }"
          allow-clear
          :status="sendParamsValidation.showError ? 'error' : 'normal'"
        />
        <template #help>
          <div v-if="sendParamsValidation.showError" class="config-help-text error">
            <a-typography-text type="danger" :style="{ fontSize: '12px' }">
              {{ sendParamsValidation.error }}
            </a-typography-text>
          </div>
          <div v-else class="config-help-text">
            <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
              用于存储从平台复制粘贴的原始查询参数，必须是有效的JSON对象格式，可以为空对象{}
            </a-typography-text>
          </div>
        </template>
      </a-form-item>

      <!-- 自定义参数 -->
      <!-- <a-divider orientation="left">
        <span class="config-section-title">自定义参数</span>
      </a-divider>

      <a-form-item label="自定义参数" field="config">
        <a-textarea
          v-model="configFieldJson"
          @input="updateConfigFieldJson"
          placeholder='请输入JSON格式的自定义扩展参数，如：{"filters": {"minFollowers": 1000}, "delay": {"min": 1000, "max": 3000}}'
          :rows="3"
          :auto-size="{ minRows: 3, maxRows: 8 }"
          allow-clear
          :status="configFieldValidation.showError ? 'error' : 'normal'"
        />
        <template #help>
          <div v-if="configFieldValidation.showError" class="config-help-text error">
            <a-typography-text type="danger" :style="{ fontSize: '12px' }">
              {{ configFieldValidation.error }}
            </a-typography-text>
          </div>
          <div v-else class="config-help-text">
            <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
              用于配置自定义的扩展参数，必须是有效的JSON对象格式，可以为空对象{}
            </a-typography-text>
          </div>
        </template>
      </a-form-item> -->

      <!-- 高级配置选项 -->
      <a-divider orientation="left">
        <span class="config-section-title">配置预览</span>
      </a-divider>

      <a-form-item label="JSON配置参数" field="configJson">
        <a-textarea
          v-model="configJson"
          placeholder='请输入JSON格式的配置参数，支持任意JSON结构，如：{"searchType": 1, "filters": {"minFollowers": 1000}, "delay": {"min": 1000, "max": 3000}}'
          :rows="4"
          :auto-size="{ minRows: 4, maxRows: 12 }"
          allow-clear
          :status="configValidation.showError ? 'error' : 'normal'"
        />

        <!-- 配置验证错误提示 -->
        <div v-if="configValidation.showError" class="config-error" :style="{ marginTop: '8px' }">
          <a-alert type="error" :message="configValidation.error" show-icon />
        </div>

        <template #extra>
          <div class="config-help">
            <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
              支持任意JSON结构：基础参数(searchType, pageSize, retries)、嵌套对象(delay,
              filters)、数组等。提交时自动验证格式。
            </a-typography-text>
            <div class="config-actions" :style="{ marginTop: '8px' }">
              <a-dropdown trigger="click" @select="handleTemplateSelect">
                <a-button size="mini" type="text">
                  插入模板
                  <icon-down />
                </a-button>
                <template #content>
                  <a-doption value="basic">基础配置</a-doption>
                  <a-doption value="xiaohongshu">小红书专用</a-doption>
                  <a-doption value="juxingtu">巨量星图专用</a-doption>
                </template>
              </a-dropdown>
              <a-button size="mini" type="text" @click="validateConfigJson"> 验证格式 </a-button>
              <a-button size="mini" type="text" @click="clearConfig"> 清空 </a-button>
              <a-button size="mini" type="text" @click="formatConfig"> 格式化 </a-button>
            </div>
          </div>
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconDown, IconCheck } from '@arco-design/web-vue/es/icon';

// 导入平台图标
import xiaohongshuIcon from '@/assets/img/xiaohongshu.png';
import douyinIcon from '@/assets/img/douyin.png';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editingTask: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:visible', 'submit', 'cancel']);

// 响应式数据
const modalVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const isEditMode = computed(() => !!props.editingTask);

// 可用平台数据
const availablePlatforms = ref([
  {
    value: 'xiaohongshu',
    name: '小红书',
    icon: xiaohongshuIcon,
    description: '生活方式分享平台，美妆、穿搭、生活达人聚集地'
  },
  {
    value: 'juxingtu',
    name: '巨量星图',
    icon: douyinIcon,
    description: '抖音官方营销平台，短视频、直播达人合作首选'
  }
]);

// 表单引用
const formRef = ref();

// 提交加载状态
const submitLoading = ref(false);

// 配置JSON的验证状态
const configValidation = reactive({
  isValid: true,
  error: null,
  showError: false
});

// 自定义参数的验证状态
const configFieldValidation = reactive({
  isValid: true,
  error: null,
  showError: false
});

// 平台查询参数的验证状态
const sendParamsValidation = reactive({
  isValid: true,
  error: null,
  showError: false
});

// 配置参数的JSON字符串（唯一数据源）
const configJson = ref('{}');

// 自定义参数的JSON字符串
// const configFieldJson = ref('{}');

// 平台查询参数的JSON字符串
const sendParamsJson = ref('{}');

// 解析后的配置对象（用于表单字段绑定）
const config = computed(() => {
  try {
    const parsed = JSON.parse(configJson.value || '{}');
    return parsed && typeof parsed === 'object' ? parsed : {};
  } catch (error) {
    return {};
  }
});

// 表单验证规则
const rules = {
  taskName: [{ required: true, message: '请输入任务名称' }],
  platform: [{ required: true, message: '请选择平台' }],
  keywords: [{ required: true, message: '请输入搜索关键词' }]
};

// 标记是否正在更新，避免无限循环
const isUpdating = ref(false);

// 更新JSON配置中的指定字段
const updateConfigField = (fieldName, value) => {
  if (isUpdating.value) return;

  try {
    isUpdating.value = true;

    const currentConfig = { ...config.value };
    currentConfig[fieldName] = value;

    configJson.value = JSON.stringify(currentConfig, null, 2);
    clearValidationErrors();
  } catch (error) {
    console.warn(`Error updating config field ${fieldName}:`, error);
    configValidation.isValid = false;
    configValidation.error = `更新字段 ${fieldName} 时出错: ${error.message}`;
    configValidation.showError = true;
  } finally {
    isUpdating.value = false;
  }
};

// 创建基础字段计算属性的工厂函数
const createConfigField = (fieldName, defaultValue) => {
  return computed({
    get: () => config.value?.[fieldName] ?? defaultValue,
    set: value => updateConfigField(fieldName, value)
  });
};

// 更新sendParams字段的JSON内容 - 平台查询参数
const updateSendParamsJson = jsonString => {
  try {
    sendParamsValidation.showError = false;

    if (!jsonString || jsonString.trim() === '') {
      // 空字符串时设置为空对象
      sendParamsField.value = {};
      sendParamsValidation.isValid = true;
      sendParamsValidation.error = null;
      return true;
    }

    const parsed = JSON.parse(jsonString);
    if (typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)) {
      // 直接使用用户输入的平台查询参数
      sendParamsField.value = parsed;
      sendParamsValidation.isValid = true;
      sendParamsValidation.error = null;
      return true;
    } else {
      sendParamsValidation.isValid = false;
      sendParamsValidation.error = '平台查询参数必须是一个JSON对象，不能是数组或基本类型';
      sendParamsValidation.showError = true;
      return false;
    }
  } catch (error) {
    sendParamsValidation.isValid = false;
    sendParamsValidation.error = `JSON格式错误: ${error.message}`;
    sendParamsValidation.showError = true;
    return false;
  }
};

// 基础任务字段的计算属性（双向绑定）
const taskName = createConfigField('taskName', '');
const platform = createConfigField('platform', '');
const priority = createConfigField('priority', 0);

// 创建config字段的计算属性工厂函数（简化版）
const createConfigSubField = (fieldName, defaultValue) => {
  return computed({
    get: () => config.value?.config?.[fieldName] ?? defaultValue,
    set: value => {
      const currentConfig = { ...config.value };
      if (!currentConfig.config) currentConfig.config = {};
      currentConfig.config[fieldName] = value;
      updateConfigField('config', currentConfig.config);
    }
  });
};

// 爬虫参数的计算属性（扁平化结构）
const keywords = createConfigSubField('keywords', '');
const maxPages = createConfigSubField('maxPages', 5);
const clawType = createConfigSubField('clawType', 1);
const searchType = createConfigSubField('searchType', 1);
const minFirstNotePlayCount = createConfigSubField('minFirstNotePlayCount', 0);
// 开启相似达人抓取
const openSimilarKol = createConfigSubField('openSimilarKol', true);
const similarKolCount = createConfigSubField('similarKolCount', 4);

const contentTypePreference = createConfigSubField('contentTypePreference', '');

// sendParams字段的计算属性
const sendParams = createConfigSubField('sendParams', {});

// config字段的特殊处理（JSON对象）- 自定义参数
const configField = computed({
  get: () => config.value?.config?.config ?? {},
  set: value => {
    const currentConfig = { ...config.value };
    if (!currentConfig.config) currentConfig.config = {};
    currentConfig.config.config = value;
    updateConfigField('config', currentConfig.config);
  }
});

// sendParams字段的特殊处理（JSON对象）- 平台查询参数
const sendParamsField = computed({
  get: () => config.value?.config?.sendParams ?? {},
  set: value => {
    const currentConfig = { ...config.value };
    if (!currentConfig.config) currentConfig.config = {};
    currentConfig.config.sendParams = value;
    updateConfigField('config', currentConfig.config);
  }
});

// 监听config字段变化，同步到JSON字符串
watch(
  () => configField.value,
  newValue => {
    if (!isUpdating.value) {
      try {
        configFieldJson.value = JSON.stringify(newValue, null, 2);
        configFieldValidation.isValid = true;
        configFieldValidation.error = null;
        configFieldValidation.showError = false;
      } catch (error) {
        console.warn('Error stringifying config field:', error);
      }
    }
  },
  { deep: true, immediate: true }
);

// 监听sendParams字段变化，同步到JSON字符串
watch(
  () => sendParamsField.value,
  newValue => {
    if (!isUpdating.value) {
      try {
        sendParamsJson.value = JSON.stringify(newValue, null, 2);
        sendParamsValidation.isValid = true;
        sendParamsValidation.error = null;
        sendParamsValidation.showError = false;
      } catch (error) {
        console.warn('Error stringifying sendParams field:', error);
      }
    }
  },
  { deep: true, immediate: true }
);

// 默认配置定义 - 扁平化的爬虫参数配置架构
const DEFAULT_CONFIG = {
  // 基础任务信息
  taskName: '',
  platform: 'xiaohongshu',
  priority: 0,

  // 扁平化的爬虫参数配置
  config: {
    // 通用爬虫参数
    maxPages: 5, // 最大爬取页数
    keywords: '', // 搜索关键词

    // 小红书平台参数
    clawType: 1, // 爬虫方式：1-笔记广场 2-内容广场（小红书专用）
    searchType: 1, // 搜索类型：1-搜笔记 0-搜昵称（小红书专用）
    minFirstNotePlayCount: 0, // 首个作品最低播放量过滤（小红书专用）
    openSimilarKol: true, // 开启相似达人抓取
    similarKolCount: 4, // 相似达人抓取数量

    // 巨量星图平台参数
    contentTypePreference: '', // 内容类型偏好（巨量星图专用）

    // 平台查询参数（用于存储从平台复制的原始查询参数）
    sendParams: {}
  }
};

// 初始化默认配置
const initializeDefaultConfig = () => {
  configJson.value = JSON.stringify(DEFAULT_CONFIG, null, 2);
};

// 重置表单
const resetForm = () => {
  clearValidationErrors();
  initializeDefaultConfig();
};

// 合并任务数据与默认配置 - 扁平化配置结构
const mergeTaskWithDefaults = task => {
  return {
    ...DEFAULT_CONFIG,
    taskName: task.taskName || DEFAULT_CONFIG.taskName,
    platform: task.platform || DEFAULT_CONFIG.platform,
    priority: task.priority || DEFAULT_CONFIG.priority,
    config: {
      ...DEFAULT_CONFIG.config,
      ...(task.config || {})
    }
  };
};

// 清除验证错误状态
const clearValidationErrors = () => {
  configValidation.isValid = true;
  configValidation.error = null;
  configValidation.showError = false;

  configFieldValidation.isValid = true;
  configFieldValidation.error = null;
  configFieldValidation.showError = false;

  sendParamsValidation.isValid = true;
  sendParamsValidation.error = null;
  sendParamsValidation.showError = false;
};

// 加载任务数据（编辑模式）
const loadTaskData = task => {
  const fullConfig = mergeTaskWithDefaults(task);
  clearValidationErrors();
  configJson.value = JSON.stringify(fullConfig, null, 2);
};

// 监听编辑任务数据变化
watch(
  () => props.editingTask,
  task => {
    if (task) {
      loadTaskData(task);
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 组件初始化时生成默认配置
if (!props.editingTask) {
  initializeDefaultConfig();
}

// 验证配置JSON格式
const validateAndParseConfig = () => {
  try {
    configValidation.showError = false;

    if (!configJson.value || configJson.value.trim() === '') {
      configValidation.isValid = true;
      configValidation.error = null;
      return true;
    }

    const parsed = JSON.parse(configJson.value);
    if (typeof parsed === 'object' && parsed !== null && !Array.isArray(parsed)) {
      configValidation.isValid = true;
      configValidation.error = null;
      return true;
    } else {
      configValidation.isValid = false;
      configValidation.error = '配置必须是一个JSON对象，不能是数组或基本类型';
      configValidation.showError = true;
      return false;
    }
  } catch (error) {
    configValidation.isValid = false;
    configValidation.error = `JSON格式错误: ${error.message}`;
    configValidation.showError = true;
    return false;
  }
};

// 平台专用字段配置 - 扁平化配置结构
const platformSpecificFields = {
  xiaohongshu: {
    // 小红书平台：保留小红书参数，重置其他平台参数
    reset: () => {
      contentTypePreference.value = ''; // 重置巨量星图参数
    }
  },
  juxingtu: {
    // 巨量星图平台：保留巨量星图参数，重置其他平台参数
    reset: () => {
      clawType.value = 1; // 重置小红书参数
      searchType.value = 1;
      minFirstNotePlayCount.value = 0;
    }
  }
};

// 选择平台
const selectPlatform = platformValue => {
  platform.value = platformValue;

  // 优雅地重置其他平台的专用字段
  Object.entries(platformSpecificFields).forEach(([key, config]) => {
    if (key !== platformValue) {
      config.reset();
    }
  });
};

// 获取平台信息
const getPlatformInfo = platformValue => {
  return availablePlatforms.value.find(p => p.value === platformValue);
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 设置加载状态
    submitLoading.value = true;

    // 1. 验证基础表单字段
    const valid = await formRef.value.validate();
    if (valid) {
      submitLoading.value = false;
      return false;
    }

    // 2. 验证配置JSON格式
    const configValid = validateAndParseConfig();
    if (!configValid) {
      Message.error(`配置参数格式错误: ${configValidation.error}`);
      submitLoading.value = false;
      return false;
    }

    // 3. 验证自定义参数JSON格式
    // const configFieldValid = updateConfigFieldJson(configFieldJson.value);
    // if (!configFieldValid) {
    //   Message.error(`自定义参数格式错误: ${configFieldValidation.error}`);
    //   return false;
    // }

    // 4. 验证平台查询参数JSON格式
    const sendParamsValid = updateSendParamsJson(sendParamsJson.value);
    if (!sendParamsValid) {
      Message.error(`平台查询参数格式错误: ${sendParamsValidation.error}`);
      submitLoading.value = false;
      return false;
    }

    // 5. 直接使用config作为提交数据
    const submitData = {
      ...config.value
    };

    // 6. 返回一个Promise，让父组件处理实际的API调用
    return new Promise(resolve => {
      // 触发提交事件，传递一个包装的resolve函数
      emit('submit', submitData, success => {
        submitLoading.value = false;
        resolve(success);
      });
    });
  } catch (error) {
    console.error('Submit error:', error);
    Message.error('提交失败');
    submitLoading.value = false;
    return false;
  }
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
  resetForm();
};

// 配置模板定义
const configTemplates = {
  basic: {
    taskName: '',
    platform: 'xiaohongshu',
    priority: 0,
    config: {
      clawType: 1,
      keywords: '',
      searchType: 1,
      maxPages: 5,
      minFirstNotePlayCount: 0
    }
  },
  xiaohongshu: {
    taskName: '',
    platform: 'xiaohongshu',
    priority: 0,
    config: {
      clawType: 1,
      keywords: '',
      searchType: 1,
      maxPages: 5,
      minFirstNotePlayCount: 0
    }
  },
  juxingtu: {
    taskName: '',
    platform: 'juxingtu',
    priority: 0,
    config: {
      clawType: 1,
      keywords: '',
      searchType: 1,
      maxPages: 5,
      minFirstNotePlayCount: 0
    }
  }
};

// 处理模板选择
const handleTemplateSelect = templateType => {
  insertConfigTemplate(templateType);
};

// 插入配置模板
const insertConfigTemplate = (templateType = 'basic') => {
  try {
    const template = configTemplates[templateType];
    if (!template) {
      Message.error('未找到指定的配置模板');
      return;
    }

    configJson.value = JSON.stringify(template, null, 2);

    // 清除验证错误状态
    configValidation.isValid = true;
    configValidation.error = null;
    configValidation.showError = false;

    Message.success(`配置模板已插入`);
  } catch (error) {
    Message.error('插入模板失败');
  }
};

// 验证JSON格式（手动验证）
const validateConfigJson = () => {
  const isValid = validateAndParseConfig();
  if (isValid) {
    Message.success('JSON格式正确');
  } else {
    Message.error(`JSON格式错误: ${configValidation.error}`);
  }
};

// 格式化配置JSON
const formatConfig = () => {
  try {
    if (!configJson.value || configJson.value.trim() === '') {
      Message.info('配置为空，无需格式化');
      return;
    }

    const parsed = JSON.parse(configJson.value);
    configJson.value = JSON.stringify(parsed, null, 2);

    // 清除验证错误状态
    configValidation.isValid = true;
    configValidation.error = null;
    configValidation.showError = false;

    Message.success('配置已格式化');
  } catch (error) {
    Message.error(`格式化失败: ${error.message}`);
  }
};

// 清空配置
const clearConfig = () => {
  configJson.value = '';

  // 清除验证错误状态
  configValidation.isValid = true;
  configValidation.error = null;
  configValidation.showError = false;

  Message.success('配置已清空');
};
</script>

<style scoped>
/* 平台选择样式 */
.platform-selection {
  display: flex;
  gap: 16px;
}

.platform-card {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border: 2px solid #e5e6eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  min-width: 280px;
  position: relative;
}

.platform-card:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
  transform: translateY(-2px);
}

.platform-card-selected {
  border-color: #165dff;
  background: #f2f5ff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.15);
}

.platform-icon {
  margin-right: 12px;
}

.platform-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 6px;
}

.platform-logo-small {
  width: 20px;
  height: 20px;
  object-fit: contain;
  border-radius: 4px;
}

.platform-info {
  flex: 1;
}

.platform-name {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 4px;
}

.platform-desc {
  font-size: 12px;
  color: #86909c;
  line-height: 1.4;
}

.platform-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

.check-icon {
  color: #165dff;
  font-size: 16px;
}

/* 配置分组样式 */
.platform-specific-config {
  width: 100%;
}

.config-section-title {
  font-weight: 600;
  color: #1d2129;
  display: flex;
  align-items: center;
  gap: 8px;
}

.xiaohongshu-config,
.juxingtu-config {
  margin-top: 16px;
}

.config-help-text {
  margin-top: 4px;
  margin-bottom: 10px;
}

.config-help-text.error {
  color: #f53f3f;
}

/* 配置参数输入区域样式 */
.config-help {
  margin-top: 4px;
}

.config-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.config-actions .arco-btn {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
}

.config-actions .arco-btn:hover {
  background-color: #f2f3f5;
}
</style>
