# 爬虫系统配置参数指南

## 📋 概述

本文档详细说明了爬虫系统支持的配置参数，包括基础配置、高级配置和平台专用配置。系统支持任意JSON结构的配置参数，提供了极大的灵活性。

## 🚀 配置机制特性

### 1. 灵活的JSON结构
- **支持任意嵌套**：可以使用复杂的嵌套对象和数组
- **动态配置**：前端配置直接传递给爬虫逻辑，无需中间转换
- **延迟验证**：在提交时进行格式验证，编辑时不限制输入
- **详细错误提示**：提供清晰的格式错误说明和修复建议

### 2. 配置优先级
1. **系统必需参数**：keywords, maxPages, crawlTaskId等
2. **用户自定义配置**：完整的config对象内容
3. **默认值参数**：只在用户未配置时使用

## 📖 配置模板

### 基础配置
```json
{
  "searchType": 1,
  "pageSize": 20,
  "retries": 3
}
```

### 高级配置
```json
{
  "searchType": 1,
  "pageSize": 20,
  "retries": 3,
  "delay": {
    "min": 1000,
    "max": 3000
  },
  "filters": {
    "minFollowers": 1000,
    "maxFollowers": 1000000
  },
  "saveVideos": true
}
```

### 小红书专用配置
```json
{
  "searchType": 1,
  "pageSize": 20,
  "retries": 3,
  "minFirstNotePlayCount": 5000,
  "delay": {
    "min": 2000,
    "max": 5000
  },
  "filters": {
    "minFollowers": 1000,
    "location": ["北京", "上海", "广州", "深圳"]
  },
  "saveVideos": true
}
```

### 巨量星图专用配置
```json
{
  "searchType": 1,
  "pageSize": 15,
  "retries": 3,
  "delay": {
    "min": 1000,
    "max": 3000
  },
  "filters": {
    "minFollowers": 5000,
    "platform": "douyin"
  },
  "saveVideos": false
}
```

### 自定义结构示例
```json
{
  "searchType": 1,
  "pageSize": 20,
  "delay": {
    "min": 1000,
    "max": 3000
  },
  "keywords": ["美妆", "护肤", "彩妆"],
  "filters": {
    "demographics": {
      "age": {
        "min": 18,
        "max": 35
      },
      "gender": "female"
    },
    "engagement": {
      "minLikes": 1000,
      "minComments": 100
    }
  },
  "saveVideos": true,
  "enableProxy": false
}
```

## 📚 配置参数详解

### 基础参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `searchType` | number | 1 | 搜索类型，1=综合搜索 |
| `pageSize` | number | 20 | 每页数量，范围1-100 |
| `retries` | number | 3 | 重试次数，范围0-10 |
| `saveVideos` | boolean | true | 是否保存视频 |

### 延迟配置

```json
{
  "delay": {
    "min": 1000,    // 最小延迟毫秒数
    "max": 3000     // 最大延迟毫秒数
  }
}
```

### 过滤配置

```json
{
  "filters": {
    "minFollowers": 1000,           // 最小粉丝数
    "maxFollowers": 1000000,        // 最大粉丝数
    "location": ["北京", "上海"],    // 地区过滤
    "gender": "female",             // 性别过滤
    "platform": "douyin"            // 平台过滤
  }
}
```

### 小红书专用参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `minFirstNotePlayCount` | number | 1 | 首个帖子最低播放量 |

## 🔧 使用方法

### 1. 前端界面操作
1. 在创建/编辑任务时，找到"配置参数"字段
2. 输入JSON格式的配置参数
3. 可以使用"插入模板"功能快速开始
4. 使用"验证格式"检查JSON语法
5. 使用"格式化"美化JSON格式

### 2. 配置验证
- **实时编辑**：支持自由编辑，不会实时验证
- **提交验证**：提交时进行完整的格式和内容验证
- **错误提示**：提供详细的错误信息和修复建议

### 3. 高级用法
- **嵌套对象**：支持多层嵌套的复杂结构
- **数组配置**：支持数组类型的配置参数
- **自定义字段**：可以添加任意自定义字段
- **条件配置**：可以根据不同条件设置不同的配置

## ⚠️ 注意事项

### 1. 格式要求
- 必须是有效的JSON格式
- 根级别必须是对象，不能是数组或基本类型
- 最大嵌套深度：10层
- 最大配置大小：50KB

### 2. 字段验证
- 数值字段会进行范围检查
- 布尔字段必须是true/false
- 对象字段不能是null或数组
- 数组字段必须是有效数组

### 3. 最佳实践
- 使用有意义的字段名
- 保持配置结构清晰
- 添加注释说明复杂配置
- 定期备份重要配置

## 🐛 故障排除

### 常见错误

1. **JSON格式错误**
   - 检查括号、引号、逗号是否匹配
   - 使用"格式化"功能检查语法

2. **字段类型错误**
   - 确保数值字段使用数字类型
   - 确保布尔字段使用true/false

3. **配置过大**
   - 简化复杂的嵌套结构
   - 移除不必要的配置项

4. **嵌套过深**
   - 减少嵌套层级
   - 使用扁平化的结构

## 📞 技术支持

如果遇到配置相关问题，请：
1. 检查控制台日志中的详细错误信息
2. 使用"验证格式"功能检查配置
3. 参考本文档的示例配置
4. 联系技术支持团队
