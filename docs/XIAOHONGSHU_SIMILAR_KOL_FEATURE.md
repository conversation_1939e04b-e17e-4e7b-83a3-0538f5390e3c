# 小红书相似达人抓取功能

## 📋 功能概述

小红书相似达人抓取功能是在现有爬虫系统基础上新增的智能扩展功能，能够在抓取目标达人信息的同时，自动发现并抓取与其相似的达人，大幅提升数据获取效率和覆盖面。

## 🚀 主要功能

### 1. 智能相似达人发现
- 基于小红书官方相似达人推荐API
- 自动分析达人特征并匹配相似用户
- 支持自定义相似达人数量

### 2. 无缝集成现有流程
- 与现有达人抓取流程完全兼容
- 相似达人使用相同的数据处理逻辑
- 支持所有现有功能（作品抓取、数据存储等）

### 3. 灵活配置控制
- 支持开启/关闭相似达人抓取
- 可配置每个主达人的相似达人数量
- 完善的错误处理机制

## 🔧 配置参数

### 基础配置
```javascript
const config = {
  // 基础爬虫配置
  keywords: '美妆',
  maxPages: 5,
  pageSize: 20,
  
  // 相似达人抓取配置
  openSimilarKol: true,      // 是否开启相似达人抓取
  similarKolCount: 4,        // 每个主达人抓取的相似达人数量
  
  // 其他配置
  saveVideos: true,
  minFirstNotePlayCount: 1000
};
```

### 配置项说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `openSimilarKol` | boolean | false | 是否开启相似达人抓取功能 |
| `similarKolCount` | number | 4 | 每个主达人抓取的相似达人数量（1-20） |

## 📊 API接口详情

### 相似达人API
- **接口地址**: `https://pgy.xiaohongshu.com/api/solar/kol/get_similar_kol`
- **请求方法**: GET
- **请求参数**:
  - `userId`: 目标达人的用户ID
  - `pageNum`: 页码（从1开始）
  - `pageSize`: 每页数量

### 响应数据结构
```javascript
{
  "code": 0,
  "success": true,
  "data": {
    "kols": [
      {
        "userId": "5939cb1a82ec39037c9453ca",
        "name": "达人昵称",
        "redId": "583646440",
        "headPhoto": "头像URL",
        "fansCount": 9317,
        "location": "浙江 杭州",
        "picturePrice": 2000,
        "videoPrice": 2500,
        // ... 更多字段
      }
    ],
    "total": 100
  }
}
```

## 💻 使用示例

### 基础使用
```javascript
const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');

async function crawlWithSimilarKol() {
  const crawler = new XiaohongshuCrawler();
  await crawler.initialize();

  const config = {
    keywords: '美妆博主',
    maxPages: 3,
    pageSize: 10,
    openSimilarKol: true,     // 开启相似达人抓取
    similarKolCount: 3,       // 每个主达人抓取3个相似达人
    saveVideos: true
  };

  const callbacks = {
    onResult: async (result) => {
      console.log(`获取达人: ${result.nickname} - 粉丝:${result.followersCount}`);
    },
    onProgress: async (progress) => {
      console.log(`进度: ${progress.percentage}%`);
    }
  };

  const results = await crawler.crawl(config, callbacks);
  console.log(`总计获取 ${results.successCount} 个达人`);
}
```

### 高级配置示例
```javascript
const config = {
  // 基础搜索配置
  keywords: '时尚穿搭',
  maxPages: 5,
  pageSize: 15,
  
  // 相似达人配置
  openSimilarKol: true,
  similarKolCount: 5,
  
  // 质量过滤
  minFirstNotePlayCount: 5000,
  
  // 其他配置
  saveVideos: true,
  crawlTaskId: 'fashion_task_001'
};
```

## 🔄 工作流程

### 1. 主达人抓取
```
搜索关键词 → 获取达人列表 → 处理每个达人详情
```

### 2. 相似达人抓取
```
主达人处理完成 → 检查配置 → 调用相似达人API → 处理相似达人详情
```

### 3. 数据处理流程
```
相似达人数据 → 构建达人信息对象 → 调用getAuthorDetail → 存储到数据库
```

## ⚠️ 注意事项

### 1. API限制
- 相似达人API可能有调用频率限制
- 建议在相似达人间设置适当延迟
- 注意Cookie的有效性和轮换

### 2. 数据质量
- 相似达人的质量取决于小红书的推荐算法
- 建议结合其他过滤条件使用
- 可能存在重复达人，系统会自动去重

### 3. 性能考虑
- 开启相似达人抓取会显著增加抓取时间
- 建议根据实际需求调整`similarKolCount`参数
- 大批量抓取时注意系统资源消耗

## 🛠️ 错误处理

### 常见错误及解决方案

1. **API调用失败**
   ```
   错误: API请求失败，状态码: 401
   解决: 检查Cookie是否有效，尝试刷新Cookie
   ```

2. **相似达人数据为空**
   ```
   错误: 没有找到相似达人
   解决: 正常情况，某些达人可能没有相似推荐
   ```

3. **处理超时**
   ```
   错误: 相似达人处理超时
   解决: 减少similarKolCount或增加延迟时间
   ```

## 📈 性能优化建议

### 1. 合理设置参数
```javascript
// 推荐配置
const config = {
  similarKolCount: 3,        // 不要设置过大
  openSimilarKol: true,      // 根据需求开启
  // 其他优化参数...
};
```

### 2. 批量处理优化
- 使用适当的延迟避免被限制
- 监控API响应时间和成功率
- 实现断点续传功能

### 3. 数据存储优化
- 利用现有的去重机制
- 合理设置数据库索引
- 定期清理无效数据

## 🧪 测试验证

### 运行功能测试
```bash
# 基础功能测试
node test/test_xiaohongshu_similar_kol.js

# 完整集成测试
node test/test_xiaohongshu_similar_kol_integration.js
```

### 测试覆盖范围
- ✅ API调用功能
- ✅ 配置参数处理
- ✅ 错误处理机制
- ✅ 数据处理流程
- ✅ 集成兼容性

## 📝 更新日志

### v1.0.0 (2024-12-06)
- ✅ 实现相似达人API调用
- ✅ 集成到现有爬虫流程
- ✅ 添加配置控制选项
- ✅ 完善错误处理机制
- ✅ 编写测试用例和文档

## 🤝 贡献指南

如需扩展或优化相似达人抓取功能，请参考以下指南：

1. 保持与现有代码风格一致
2. 添加适当的错误处理
3. 编写相应的测试用例
4. 更新相关文档

## 📞 技术支持

如遇到问题或需要技术支持，请：
1. 查看错误日志和调试信息
2. 参考本文档的错误处理部分
3. 运行测试用例验证功能状态
