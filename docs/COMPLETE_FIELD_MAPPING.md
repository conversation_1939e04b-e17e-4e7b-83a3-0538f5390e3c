# 完整表单字段映射机制

## 📋 功能概述

实现了CrawlerTaskModal.vue组件中所有表单字段与configJson的完整双向映射机制，确保任何表单字段的变化都能实时同步到JSON配置中。

## 🎯 映射字段列表

### 基础任务字段
| 表单字段 | JSON字段 | 类型 | 默认值 | 映射规则 |
|---------|---------|------|--------|----------|
| `form.taskName` | `taskName` | string | '' | 非空时映射，空时删除 |
| `form.platform` | `platform` | string | '' | 非空时映射，空时删除 |
| `form.keywords` | `keywords` | string | '' | 非空时映射，空时删除 |
| `form.maxPages` | `maxPages` | number | 5 | 非默认值时映射，默认值时删除 |
| `form.priority` | `priority` | number | 0 | 非默认值时映射，默认值时删除 |

### 平台专用字段
| 表单字段 | JSON字段 | 类型 | 默认值 | 映射规则 |
|---------|---------|------|--------|----------|
| `form.minFirstNotePlayCount` | `minFirstNotePlayCount` | number | 0 | 大于0时映射，0时删除 |
| `form.contentTypePreference` | `contentTypePreference` | string | '' | 非空时映射，空时删除 |

### 爬取类型字段
| 表单字段 | JSON字段 | 类型 | 默认值 | 映射规则 |
|---------|---------|------|--------|----------|
| `form.clawType` | `clawType` | number | 1 | 非默认值时映射，默认值时删除 |
| `form.searchType` | `searchType` | number | 1 | 非默认值时映射，默认值时删除 |

### 通用配置字段
| 表单字段 | JSON字段 | 类型 | 说明 |
|---------|---------|------|------|
| `form.config.*` | `*` | any | 用户手动输入的其他配置参数 |

## 🔧 技术实现

### 1. 同步函数核心逻辑

```javascript
const syncPlatformFieldsToConfigJson = () => {
  // 防循环机制
  if (isUpdating.value) return;
  
  try {
    isUpdating.value = true;
    
    // 解析现有配置
    let currentConfig = {};
    if (configJson.value && configJson.value.trim()) {
      currentConfig = JSON.parse(configJson.value);
    }
    
    const updatedConfig = { ...currentConfig };
    
    // 映射所有表单字段
    // 基础字段
    if (form.taskName && form.taskName.trim()) {
      updatedConfig.taskName = form.taskName;
    } else {
      delete updatedConfig.taskName;
    }
    
    // ... 其他字段映射
    
    // 合并用户自定义配置
    if (form.config && typeof form.config === 'object') {
      Object.keys(form.config).forEach(key => {
        if (!isFormField(key)) {
          updatedConfig[key] = form.config[key];
        }
      });
    }
    
    // 更新JSON字符串
    configJson.value = JSON.stringify(updatedConfig, null, 2);
    
  } finally {
    isUpdating.value = false;
  }
};
```

### 2. 字段监听器

```javascript
// 为每个表单字段添加监听器
watch(() => form.taskName, () => syncPlatformFieldsToConfigJson());
watch(() => form.platform, () => syncPlatformFieldsToConfigJson());
watch(() => form.keywords, () => syncPlatformFieldsToConfigJson());
watch(() => form.maxPages, () => syncPlatformFieldsToConfigJson());
watch(() => form.priority, () => syncPlatformFieldsToConfigJson());
watch(() => form.minFirstNotePlayCount, () => syncPlatformFieldsToConfigJson());
watch(() => form.contentTypePreference, () => syncPlatformFieldsToConfigJson());
watch(() => form.clawType, () => syncPlatformFieldsToConfigJson());
watch(() => form.searchType, () => syncPlatformFieldsToConfigJson());
```

### 3. 反向同步机制

```javascript
const validateAndParseConfig = () => {
  // JSON解析和验证
  const parsed = JSON.parse(configJson.value);
  
  if (!isUpdating.value) {
    isUpdating.value = true;
    
    // 分离所有字段
    const { 
      taskName, platform, keywords, maxPages, priority,
      minFirstNotePlayCount, contentTypePreference, 
      clawType, searchType, 
      ...generalConfig 
    } = parsed;
    
    // 更新表单字段
    if (taskName !== undefined) form.taskName = taskName;
    if (platform !== undefined) form.platform = platform;
    // ... 其他字段更新
    
    // 更新通用配置
    form.config = generalConfig;
    
    isUpdating.value = false;
  }
};
```

## 📊 数据流向图

```
┌─────────────────┐    字段监听    ┌─────────────────┐
│  表单字段        │ ──────────→   │  同步函数        │
│ • taskName      │               │ syncPlatform... │
│ • platform      │               │                 │
│ • keywords      │               │ 映射规则：       │
│ • maxPages      │               │ • 非空值映射     │
│ • priority      │               │ • 默认值删除     │
│ • minFirst...   │               │ • 保留用户配置   │
│ • contentType.. │               └─────────────────┘
│ • clawType      │                        │
│ • searchType    │                        ↓
└─────────────────┘                ┌─────────────────┐
        ↑                          │  configJson     │
        │                          │  完整JSON配置   │
        │                          │                 │
        │                          │ {               │
        │                          │   "taskName":..│
        │                          │   "platform":..│
        │                          │   "keywords":..│
        │                          │   "maxPages":..│
        │                          │   "priority":..│
        │                          │   "clawType":..│
        │                          │   "searchType":│
        │                          │   "custom": ... │
        │                          │ }               │
        │                          └─────────────────┘
        │                                  │
        │                                  ↓
        │                          ┌─────────────────┐
        │                          │  JSON解析       │
        │                          │  字段分离       │
        │                          │  反向同步       │
        │                          └─────────────────┘
        │                                  │
        └──────────── 表单更新 ←──────────────┘
```

## 🔄 同步场景示例

### 场景1：用户填写基础信息
```javascript
// 用户操作
form.taskName = "美妆达人采集";
form.platform = "xiaohongshu";
form.keywords = "美妆 护肤";

// 自动同步到JSON
{
  "taskName": "美妆达人采集",
  "platform": "xiaohongshu", 
  "keywords": "美妆 护肤"
}
```

### 场景2：用户设置高级配置
```javascript
// 用户操作
form.maxPages = 10;
form.priority = 5;
form.minFirstNotePlayCount = 5000;

// 自动同步到JSON
{
  "taskName": "美妆达人采集",
  "platform": "xiaohongshu",
  "keywords": "美妆 护肤",
  "maxPages": 10,
  "priority": 5,
  "minFirstNotePlayCount": 5000
}
```

### 场景3：用户直接编辑JSON
```javascript
// 用户在JSON编辑器中输入
{
  "taskName": "时尚博主",
  "platform": "juxingtu",
  "keywords": "时尚 穿搭",
  "maxPages": 15,
  "clawType": 2,
  "searchType": 0,
  "customConfig": {
    "delay": { "min": 1000, "max": 3000 }
  }
}

// 自动解析并更新表单
form.taskName = "时尚博主";
form.platform = "juxingtu";
form.keywords = "时尚 穿搭";
form.maxPages = 15;
form.clawType = 2;
form.searchType = 0;
form.config = { customConfig: { delay: { min: 1000, max: 3000 } } };
```

## ✅ 映射规则详解

### 1. 字符串字段映射
```javascript
// 规则：非空字符串时映射，空字符串时删除
if (form.taskName && form.taskName.trim()) {
  updatedConfig.taskName = form.taskName;
} else {
  delete updatedConfig.taskName;
}
```

### 2. 数值字段映射
```javascript
// 规则：非默认值时映射，默认值时删除
if (form.maxPages && form.maxPages !== 5) {
  updatedConfig.maxPages = form.maxPages;
} else {
  delete updatedConfig.maxPages;
}
```

### 3. 特殊字段映射
```javascript
// minFirstNotePlayCount：大于0时映射
if (form.minFirstNotePlayCount > 0) {
  updatedConfig.minFirstNotePlayCount = form.minFirstNotePlayCount;
} else {
  delete updatedConfig.minFirstNotePlayCount;
}
```

### 4. 用户配置保护
```javascript
// 保护用户手动输入的配置，不被表单字段覆盖
Object.keys(form.config).forEach(key => {
  if (!isFormField(key)) {
    updatedConfig[key] = form.config[key];
  }
});
```

## 🛡️ 安全机制

### 1. 无限循环防护
- 使用`isUpdating`标记防止循环更新
- 确保同步过程中不触发新的监听器

### 2. 错误容错处理
- JSON解析失败时使用空对象
- 不影响用户的现有操作

### 3. 数据完整性保护
- 分离表单字段和用户配置
- 避免意外覆盖用户数据

## 📝 使用效果

### 用户体验
1. **实时同步**：任何表单字段变化立即反映到JSON
2. **双向编辑**：可通过表单或JSON编辑器修改
3. **智能清理**：默认值自动清理，保持JSON简洁
4. **配置保护**：用户手动配置不会被覆盖

### 开发维护
1. **完整映射**：所有字段都有明确的映射规则
2. **易于扩展**：新增字段只需添加对应的映射逻辑
3. **调试友好**：清晰的数据流向和错误处理
4. **性能优化**：避免不必要的更新和解析

这个完整的字段映射机制确保了表单与JSON配置的完全同步，为用户提供了灵活而强大的配置编辑体验！
