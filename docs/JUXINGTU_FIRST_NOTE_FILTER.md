# 巨量星图爬虫首个帖子播放量过滤功能

## 📋 功能概述

本文档详细说明了巨量星图爬虫新增的"首个帖子播放量过滤"功能，该功能可以在爬取达人信息时，根据达人首个帖子的播放量进行筛选，提高爬取数据的质量。

## 🚀 主要功能

### 1. 新增配置参数
- **参数名**: `minFirstNotePlayCount`
- **类型**: `number`
- **默认值**: `0`（不过滤）
- **说明**: 用于过滤达人的首个帖子播放量阈值

### 2. 过滤流程
1. 获取达人的视频列表数据
2. 提取第一个视频的videoId
3. 调用巨量星图API获取视频详细信息
4. 从详细数据中获取`video_info.vv_cnt`字段（播放量）
5. 判断播放量是否满足配置的阈值要求
6. 满足条件则继续处理，否则跳过该达人

### 3. 错误处理
- 视频列表为空的情况
- 首个视频缺少videoId的情况
- API调用失败的情况
- 使用备用数据进行兜底处理

## 🔧 使用方法

### 基本用法

```javascript
const crawler = new XingtuCrawler();
await crawler.initialize();

const config = {
  keywords: '美妆',
  maxPages: 5,
  pageSize: 20,
  minFirstNotePlayCount: 10000, // 设置首个帖子最低播放量为1万
  saveVideos: true
};

const results = await crawler.crawl(config, callbacks);
```

### 配置说明

```javascript
const crawlConfig = {
  // 基础配置
  keywords: '搜索关键词',
  maxPages: 10,
  pageSize: 20,
  
  // 新增：首个帖子播放量过滤
  minFirstNotePlayCount: 5000, // 首个帖子最低播放量要求
  
  // 其他配置
  saveVideos: true,
  crawlTaskId: 'task_123'
};
```

### 不同阈值的建议

```javascript
// 低质量过滤（适用于初步筛选）
minFirstNotePlayCount: 1000

// 中等质量过滤（适用于一般需求）
minFirstNotePlayCount: 5000

// 高质量过滤（适用于精品内容）
minFirstNotePlayCount: 10000

// 顶级质量过滤（适用于头部达人）
minFirstNotePlayCount: 50000

// 不进行过滤（获取所有达人）
minFirstNotePlayCount: 0
```

## 📊 实现细节

### 1. 核心方法

#### `checkFirstNotePlayCount(authorId, minPlayCount)`
检查达人首个帖子播放量是否满足条件

**参数**:
- `authorId`: 达人ID
- `minPlayCount`: 最低播放量要求

**返回值**:
```javascript
{
  passed: boolean,           // 是否通过检查
  firstNotePlayCount: number, // 首个帖子播放量
  noteId: string,            // 帖子ID
  reason: string             // 检查结果说明
}
```

#### `fetchJuxingtuNoteDetail(videoId)`
获取巨量星图视频详细信息

**参数**:
- `videoId`: 视频ID

**返回值**:
- 视频详细数据对象，包含`video_info.vv_cnt`等字段

### 2. API接口

**接口地址**: `https://www.xingtu.cn/gw/api/data_sp/item_report_detail`
**请求方法**: `POST`
**请求参数**:
```json
{
  "item_id": "视频ID"
}
```

**响应数据结构**:
```json
{
  "data": {
    "video_info": {
      "vv_cnt": 125000,      // 播放量
      "like_cnt": 5420,      // 点赞数
      "comment_cnt": 89,     // 评论数
      "share_cnt": 234,      // 分享数
      "collect_cnt": 1250    // 收藏数
    }
  }
}
```

### 3. 数据流程

```
达人列表 → 获取视频数据 → 提取首个视频ID → 获取视频详情 → 检查播放量 → 决定是否继续处理
```

### 4. 日志输出

功能运行时会输出详细的日志信息：

```
🎯 首个帖子播放量过滤阈值: 10000
🔍 检查达人 xxx 首个帖子播放量，要求阈值: 10000
📊 达人 xxx 首个视频播放量检查: 15000 ≥ 10000
✅ 达人 xxx 首个帖子播放量检查通过: 15000
```

或者跳过的情况：

```
⚠️ 达人 xxx 首个帖子播放量不满足条件，跳过处理
   首个帖子播放量: 3000, 要求阈值: 10000
```

## 🧪 测试验证

### 运行测试

```bash
node test/test_juxingtu_first_note_filter.js
```

### 运行示例

```bash
node examples/juxingtu_first_note_filter_example.js
```

### 测试内容

1. **完整流程测试**: 测试整个爬取流程中的过滤功能
2. **单独方法测试**: 测试`checkFirstNotePlayCount`方法
3. **边界情况测试**: 测试各种异常情况的处理
4. **配置参数测试**: 测试不同阈值的过滤效果

## 🔄 与小红书爬虫的一致性

### 架构一致性
- ✅ 相同的参数名称：`minFirstNotePlayCount`
- ✅ 相同的方法名称：`checkFirstNotePlayCount`
- ✅ 相同的返回结构：`{passed, firstNotePlayCount, noteId, reason}`
- ✅ 相同的集成方式：在`processAuthorsInBatches`中调用
- ✅ 相同的错误处理：备用数据机制

### 功能差异
- **数据源**: 巨量星图使用`video_info.vv_cnt`，小红书使用`readNum`
- **API接口**: 巨量星图使用`item_report_detail`，小红书使用`note/detail`
- **数据结构**: 巨量星图获取视频数据，小红书获取笔记数据

## 📈 性能优化

### 1. 延迟控制
- 获取视频详情前添加1-2秒随机延迟
- 跳过的达人使用较短延迟（1-2秒）
- 正常处理的达人使用标准延迟（3-5秒）

### 2. 备用数据机制
- API调用失败时使用视频列表中的播放量数据
- 确保过滤功能的可靠性和稳定性

### 3. 错误处理
- 完整的异常捕获和处理
- 详细的错误日志输出
- 不影响整体爬取流程的稳定性

## 🎯 使用建议

1. **根据业务需求设置合适的阈值**
   - 品牌合作：建议10000+
   - 内容筛选：建议5000+
   - 数据收集：建议1000+

2. **监控过滤效果**
   - 关注成功率变化
   - 调整阈值以平衡质量和数量

3. **结合其他指标**
   - 可以与粉丝数、播放量中位数等指标结合使用
   - 构建更完整的达人质量评估体系

## 🔧 故障排除

### 常见问题

1. **过滤率过高**
   - 降低`minFirstNotePlayCount`阈值
   - 检查目标关键词的达人质量分布

2. **API调用失败**
   - 检查Cookie是否有效
   - 确认网络连接状态
   - 查看错误日志获取详细信息

3. **性能问题**
   - 适当增加延迟时间
   - 减少并发处理数量
   - 监控API调用频率

## 📝 更新日志

### v1.0.0 (2024-01-15)
- ✅ 新增`minFirstNotePlayCount`配置参数
- ✅ 实现`checkFirstNotePlayCount`过滤方法
- ✅ 实现`fetchJuxingtuNoteDetail`详情获取方法
- ✅ 集成到现有爬虫流程中
- ✅ 添加完整的错误处理和日志输出
- ✅ 与小红书爬虫保持架构一致性
