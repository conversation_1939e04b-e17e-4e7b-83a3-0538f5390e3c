# 平台颜色方案设计

## 概述

为了提升用户体验和视觉区分度，系统为不同的平台设置了独特的颜色标识。每个平台的颜色都经过精心设计，既考虑了品牌识别度，又确保了良好的视觉对比度。

## 颜色方案

### 主要平台颜色

| 平台 | 颜色代码 | 颜色预览 | 设计理念 |
|------|----------|----------|----------|
| 小红书 | `#ff4d4f` | 🔴 红色 | 小红书品牌主色调 |
| 抖音/巨量星图 | `#1890ff` | 🔵 蓝色 | 抖音科技感蓝色 |
| 快手 | `#fa8c16` | 🟠 橙色 | 快手活力橙色 |
| 微博 | `#eb2f96` | 🩷 粉红色 | 微博社交粉色 |
| B站 | `#13c2c2` | 🩵 青色 | B站特色青色 |
| 知乎 | `#52c41a` | 🟢 绿色 | 知乎知识绿色 |
| 淘宝 | `#fa541c` | 🟠 深橙色 | 淘宝购物橙色 |
| 微信&视频号 | `#389e0d` | 🟢 深绿色 | 微信品牌绿色 |
| 其他 | `#8c8c8c` | ⚫ 灰色 | 中性灰色 |
| 置换 | `#722ed1` | 🟣 紫色 | 特殊业务紫色 |

## 设计原则

### 1. 品牌一致性
- 尽可能使用各平台的品牌主色调
- 保持与用户对平台的色彩认知一致

### 2. 视觉区分度
- 确保不同平台颜色有明显差异
- 避免相近颜色造成混淆

### 3. 可访问性
- 所有颜色都通过了对比度测试
- 支持色盲用户的识别需求

### 4. 系统一致性
- 在所有页面和组件中使用统一的颜色方案
- 表格、标签、图表等都采用相同的颜色映射

## 技术实现

### 工具函数

系统提供了两个主要的颜色工具函数：

1. **`getPlatformColor(platform)`** - 原有平台字段的颜色映射
2. **`getCooperationPlatformColor(cooperationData)`** - 合作对接数据的平台颜色映射

### 使用示例

```javascript
// 在Vue组件中使用
<a-tag :color="getPlatformColor(record.platform)">
  {{ getPlatformName(record.platform) }}
</a-tag>

// 合作对接数据
<a-tag :color="getCooperationPlatformColor(record)">
  {{ getCooperationPlatformText(record, dictionaryOptions) }}
</a-tag>
```

## 应用范围

### 已应用的组件和页面

1. **达人提报视图** (`InfluencerReportView.vue`)
   - 表格平台列
   - 详情页面平台显示
   - 平台筛选器

2. **合作对接管理** (`CooperationView.vue`)
   - 表格平台列
   - 筛选器选项

3. **合作对接详情** (`CooperationDataModal.vue`)
   - 基本信息平台显示

4. **达人管理** (`InfluencerView.vue`)
   - 表格平台列

### 颜色映射逻辑

系统采用优先级映射逻辑：

1. **合作对接数据**：
   - 优先使用 `publishPlatform`（发布平台）
   - 备选使用 `seedingPlatform`（种草平台）
   - 降级使用 `platform`（原有字段）

2. **达人提报数据**：
   - 主要使用 `platform` 字段
   - 支持合作对接字段的扩展

## 维护指南

### 添加新平台

1. 在 `platformUtils.js` 中添加颜色映射
2. 在 `cooperationPlatformUtils.js` 中添加对应映射
3. 更新本文档的颜色对照表
4. 测试所有相关页面的显示效果

### 修改现有颜色

1. 确保新颜色符合设计原则
2. 同时更新两个工具函数文件
3. 测试对比度和可访问性
4. 更新文档说明

## 注意事项

- 修改颜色时需要同时更新所有相关的工具函数
- 新增平台时要考虑与现有颜色的区分度
- 定期检查颜色在不同设备和浏览器上的显示效果
- 保持与设计规范的一致性
